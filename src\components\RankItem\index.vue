<template>
  <div class="gift-rank-box">
    <div class="top-box">
      <div class="rank-top" v-if="topList[1]">
        <div class="logo">
          <DefaultLogo type="player" :logo="topList[1]?.userImage" width="60px" height="60px"></DefaultLogo>
          <img class="badge" src="@/static/live/<EMAIL>" />
        </div>
        <div class="info">
          <div class="name">{{ topList[1]?.userName }}</div>
          <img class="sex" v-if="topList[1]?.sex === 'boy'" src="@/static/live/man-icon.png" />
          <img class="sex" v-if="topList[1]?.sex === 'girl'" src="@/static/live/woman-icon.png" />
        </div>
        <LevelItem class="level-icon" v-if="type === 'contribute'" :level="topList[1]?.grade" />
        <div class="hot">
          <img v-if="type === 'popularity'" src="@/static/live/gx-hot-icon.png" />
          <img v-if="type === 'ticket'" src="@/static/live/qp-hot-icon.png" />
          <img v-if="type === 'contribute'" src="@/static/live/red-hot-icon.png" />{{ topList[1]?.num }}
        </div>
      </div>
      <div class="rank-top" v-if="topList[0]">
        <div class="logo">
          <DefaultLogo type="player" :logo="topList[0]?.userImage" width="60px" height="60px"></DefaultLogo>
          <img class="badge" src="@/static/live/<EMAIL>" />
        </div>
        <div class="info">
          <div class="name">{{ topList[0]?.userName }}</div>
          <img class="sex" v-if="topList[0]?.sex === 'boy'" src="@/static/live/man-icon.png" />
          <img class="sex" v-if="topList[0]?.sex === 'girl'" src="@/static/live/woman-icon.png" />
        </div>
        <LevelItem class="level-icon" v-if="type === 'contribute'" :level="topList[0]?.grade" />
        <div class="hot">
          <img v-if="type === 'popularity'" src="@/static/live/gx-hot-icon.png" />
          <img v-if="type === 'ticket'" src="@/static/live/qp-hot-icon.png" />
          <img v-if="type === 'contribute'" src="@/static/live/red-hot-icon.png" />{{ topList[0]?.num }}
        </div>
      </div>
      <div class="rank-top" v-if="topList[2]">
        <div class="logo">
          <DefaultLogo type="player" :logo="topList[2]?.userImage" width="60px" height="60px"></DefaultLogo>
          <img class="badge" src="@/static/live/<EMAIL>" />
        </div>
        <div class="info">
          <div class="name">{{ topList[2]?.userName }}</div>
          <img class="sex" v-if="topList[2]?.sex === 'boy'" src="@/static/live/man-icon.png" />
          <img class="sex" v-if="topList[2]?.sex === 'girl'" src="@/static/live/woman-icon.png" />
        </div>
        <LevelItem class="level-icon" v-if="type === 'contribute'" :level="topList[2]?.grade" />
        <div class="hot">
          <img v-if="type === 'popularity'" src="@/static/live/gx-hot-icon.png" />
          <img v-if="type === 'ticket'" src="@/static/live/qp-hot-icon.png" />
          <img v-if="type === 'contribute'" src="@/static/live/red-hot-icon.png" />{{ topList[2]?.num }}
        </div>
      </div>
    </div>
    <div class="line"></div>
    <div class="list-box">
      <div class="item" v-for="(item, index) in list">
        <span class="num">{{ index + 4 }}</span>
        <div class="info">
          <DefaultLogo type="player" :logo="item.userImage" width="40px" height="40px"></DefaultLogo>
          <LevelItem class="level-icon" v-if="type === 'contribute'" :level="item.grade" />
          <div class="name">{{ item.userName }}</div>
          <!-- <img class="sex" v-if="item.sex === 'boy'" src="@/static/live/man-icon.png" />
          <img class="sex" v-if="item.sex === 'girl'" src="@/static/live/woman-icon.png" /> -->
        </div>
        <div class="hot">
          <img v-if="type === 'popularity'" src="@/static/live/gx-hot-icon.png" />
          <img v-if="type === 'ticket'" src="@/static/live/qp-hot-icon.png" />
          <img v-if="type === 'contribute'" src="@/static/live/red-hot-icon.png" />{{ item.num }}
        </div>
      </div>
    </div>
    <Empty v-if="publicList.length === 0" class="empty" />
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  publicList: {
    type: Array,
    default: () => []
  },
  type: {
    type: String,
    default: 'popularity'
  }
})

interface PublicListType {
  grade: number
  userName: string
  userImage: string
  num: number
  sex: string
}

let topList = ref<PublicListType[]>([])
let list = ref<PublicListType[]>([])
watch(() => props.publicList, (newData, oldData) => {
  // if (newData.length === 0) return
  topList.value = newData.slice(0, 3) as PublicListType[]
  list.value = newData.slice(3) as PublicListType[]
}, { immediate: true, deep: true })
</script>

<style lang="scss" scoped>
.gift-rank-box {
  padding-top: $bf-public-size-10;
  box-sizing: border-box;
}

.top-box {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  gap: $bf-public-size-8;

  .rank-top {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    padding-top: $bf-public-size-12;
    border-radius: $bf-public-size-16 $bf-public-size-16 0px 0px;
    background: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    max-width: 120px;

    // 第二名 (左侧)
    &:nth-child(1) {
      height: 160px;

      .logo {
        border-color: #CFE9F1;
      }
    }

    // 第一名 (中间，最高)
    &:nth-child(2) {
      height: 180px;
      margin-top: 0;

      .badge {
        width: $bf-public-size-44;
        height: $bf-public-size-28;
      }

      .logo {
        border-color: #F9E38B;
      }
    }

    // 第三名 (右侧)
    &:nth-child(3) {
      height: 160px;

      .logo {
        border-color: #F1AD70;
      }
    }

    .logo {
      position: relative;
      margin-bottom: $bf-public-size-10;
      border-radius: 50%;
      border: $bf-public-size-2 solid;

      img:first-child {
        width: $bf-public-size-60;
        height: $bf-public-size-60;
        border-radius: 50%;
        object-fit: cover;
        border: $bf-public-size-3 solid #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      .badge {
        position: absolute;
        bottom: -15px;
        right: -13px;
        transform: translateX(-50%);
        width: $bf-public-size-44;
        height: $bf-public-size-28;
      }
    }

    .info {
      display: flex;
      align-items: center;
      gap: $bf-public-size-2;
      font-size: $bf-font-size-base;
      font-weight: 500;
      color: $bf-text-dark;
      margin: $bf-public-size-2 0;
      max-width: 90px;

      .name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin: $bf-public-size-2 0;
        line-height: $bf-public-size-24;
      }

      .sex {
        width: $bf-public-size-16;
        height: $bf-public-size-16;
      }
    }

    .level-icon {
      margin-bottom: $bf-public-size-3;
    }

    .hot {
      display: flex;
      align-items: center;
      gap: $bf-public-size-4;
      font-size: $bf-font-size-sm;
      color: #FF4757;
      font-weight: bold;

      img {
        width: $bf-public-size-12;
        height: $bf-public-size-12;
      }
    }
  }
}

.line {
  height: $bf-public-size-8;
  background: $bf-bg-primary;
}

.list-box {
  // height: calc(100vh - 656px);
  // overflow-y: auto;

  .item {
    display: flex;
    align-items: center;
    padding: $bf-public-size-8 0;
    margin: 0 $bf-public-size-16;
    background: white;
    border-bottom: 1px solid transparent;

    &:not(:last-child) {
      border-color: #F5F5F5;
    }

    .num {
      font-size: $bf-font-size-base;
      color: $bf-text-black;
      line-height: $bf-public-size-24;
      min-width: $bf-public-size-24;
      text-align: center;
      margin: 0 $bf-public-size-8;
    }

    .info {
      display: flex;
      align-items: center;
      flex: 1;

      .level-icon {
        margin-left: $bf-public-size-8;
      }

      .name {
        font-size: $bf-font-size-base;
        font-weight: 400;
        color: $bf-text-black;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        max-width: 120px;
      }

      .sex {
        width: $bf-public-size-16;
        height: $bf-public-size-16;
      }
    }

    .hot {
      display: flex;
      align-items: center;
      gap: $bf-public-size-4;
      font-size: $bf-font-size-sm;
      color: $bf-color-primary;

      img {
        width: $bf-public-size-12;
        height: $bf-public-size-12;
      }
    }
  }
}

.empty {
  height: calc(100vh - 468px);
}
</style>