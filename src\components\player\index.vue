<template>
  <div class="player-wrap">
    <!-- 播放器 -->
    <div id="dplayer" :style="customStyle"></div>
    <!-- <div v-if="isReconnecting" class="reconnecting-overlay">
      <div class="reconnecting-message">
        <div class="loading-spinner"></div>
        
      </div>
    </div> -->
    <!-- <p>连接中断，正在重新连接...</p> -->
    <!-- 移动端清晰度切换按钮 -->
    <div class="mobile-quality-btn" @click="toggleDefinitionList" v-if="showControls">
      {{ currentDefinition }}
    </div>
    <!-- 清晰度列表  -->
    <div class="mobile-quality-list" v-if="showDefinitionList">
      <div v-for="item in definitionList" :key="item.definition" class="mobile-quality-item"
        :class="{ 'active': currentDefinition === item.definition }" @click="changeDefinition(item)">
        {{ item.definition }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onBeforeUnmount } from "vue";
import Player, { Events } from 'xgplayer';
import Danmu from 'xgplayer/es/plugins/danmu'
import definition from 'xgplayer/es/plugins/definition'
import 'xgplayer/es/plugins/danmu/index.css'
import HlsPlugin from 'xgplayer-hls';
import 'xgplayer/dist/index.min.css';
import { useUserStore } from "@/stores/user";
import { showToast } from 'vant';

let customStyle = ref({
  cursor: 'default'
})
interface RtmpsType {
  definition: string;
  url: string;
  text: string;
}
const props = defineProps<{ rtmps: RtmpsType[] }>();
// 用户状态管理
const userStore = useUserStore()
const isLogin = computed(() => userStore.isLogin)

// 自定义清晰度切换相关
const definitionList = ref<Array<{ definition: string, url: string, text: { zh: string } }>>([])
const currentDefinition = ref('高清')
const showDefinitionList = ref(false)
// const isReconnecting = ref(false)

// 自定义控制栏相关
const showControls = ref(false)
const isPlaying = ref(false)
const volume = ref(0.6)
const isMuted = ref(false)
const isFullscreen = ref(false)
const isDanmuOpen = ref(true)

// 播放器实例和 HLS 实例的引用
let player: any = null;
// let hlsInstance: any = null;
interface DanmuItem {
  duration: 5000,
  id: string,
  txt: string
}

let danmuComments: DanmuItem[] = []
const handleSendDanmu = (danmu) => {
  console.log(danmu, 'danmu')
  if (player) {
    player.danmu.sendComment({
      duration: 5000,
      id: danmu.messageId,
      txt: danmu.content,
      style: {                 //弹幕自定义样式
        color: '#fff',         //例：'#ff9500',
        fontSize: '18px',      // 例：'20px',
        padding: '2px 10px',        //例： 2px 11px',
        textShadow: '2px 2px 4px #000000'
      }
    })
  }
}

// 切换清晰度列表显示
const toggleDefinitionList = () => {
  showDefinitionList.value = !showDefinitionList.value;
}

//弹幕控制
const toggleDanmu = () => {
  if (!player) return
  if (isDanmuOpen.value) {
    player.danmu.stop()
    isDanmuOpen.value = false
  } else {
    player.danmu.start()
    isDanmuOpen.value = true
  }
}


const initPlayer = () => {
  const container = document.getElementById('dplayer');
  const playerWarp = document.getElementById('dplayer-warp');
  if (!container) return;

  // 准备清晰度配置数据，确保 matchLiveClearList 存在且是数组
  const clearList = Array.isArray(props.rtmps) ? props.rtmps : [];
  const definitionListData = clearList.map(item => {
    // let url = item.liveUrl.replace("m3u8", "flv");
    return {
      definition: item.definition || '标清', // 清晰度标识
      url: item.url || '',        // 播放地址
      text: {// 显示文本
        zh: item.definition || '标清' // 显示文本
      }
    }
  });
  // 更新自定义清晰度列表
  definitionList.value = definitionListData
  // 获取默认播放地址，优先使用高清，否则使用pullUrl
  let defaultUrl = '';
  let defaultDefinition = '高清';
  let defaultIndex = definitionListData.findIndex((item) => {
    return item.definition === '高清'
  })
  if (defaultIndex !== -1) {
    defaultUrl = definitionListData[defaultIndex].url
    defaultDefinition = definitionListData[defaultIndex].definition
  } else {
    const defaultItem = props.rtmps.find(item => item.definition === '高清') || props.rtmps[0];
    defaultUrl = defaultItem.url;
    // defaultUrl = liveDetail.pullUrl.replace("m3u8", "flv") || ''
    if (definitionListData.length > 0) {
      defaultDefinition = definitionListData[0].definition
    }
  }

  // 如果没有有效的播放地址，则不初始化播放器
  if (!defaultUrl) {
    console.warn('没有有效的播放地址，无法初始化播放器')
    return;
  }

  // 设置当前清晰度
  currentDefinition.value = defaultDefinition
  console.log(defaultDefinition, defaultUrl, definitionListData, '------definitionListData')
  // 播放器配置
  const playerConfig = {
    el: container,
    isLive: true,
    autoplay: true,
    playsinline: true,
    playbackRate: false,
    autoplayMuted: false,
    fluid: true,
    lang: 'zh-cn',
    url: defaultUrl,
    ignores: [], // 移除忽略项，确保所有控制元素都能显示
    fullscreenTarget: playerWarp as HTMLElement,
    // definition: {
    //   defaultDefinition: currentDefinition.value,
    //   list: definitionListData,
    //   isShowIcon: true,
    // }
  }
  //弹幕配置
  if (document.createElement('video').canPlayType('application/vnd.apple.mpegurl')) {
    console.log('原生支持 hls 播放')
    // 原生支持 hls 播放
    player = new Player({
      ...playerConfig,
      plugins: [definition]
    })
  } else if (HlsPlugin.isSupported()) {
    // 使用 HLS 插件
    player = new Player({
      ...playerConfig,
      plugins: [HlsPlugin],
      // Danmu
      danmu: {
        comments: danmuComments,
        fontSize: 18,
        defaultOpen: true,
        channelSize: 28,
        style: {
          textShadow: '2px 2px 4px #000000'
        }
      }
    })
  }
  // 添加事件监听
  if (player) {
    // 监听清晰度切换事件
    player.on('definition_change', (data: any) => {
      console.log('清晰度切换事件:', data)
      // 检查权限
      if (data && data.to && !checkQualityPermission(data.to.definition)) {
        // 阻止切换到蓝光
        showToast('观看蓝光画质需要登录，请先登录')
        // 切换回之前的清晰度
        if (data.from && data.from.definition) {
          setTimeout(() => {
            const definitionPlugin = player.getPlugin('definition')
            if (definitionPlugin) {
              definitionPlugin.changeDefinition(data.from)
            }
          }, 100)
        }
        return
      }
      if (data && data.to) {
        const definitionText = data.to.definition || '高清';
        showToast(`已切换到${definitionText}画质`)
      }
    })

    // 监听播放器就绪事件
    player.on('ready', () => {
      console.log('播放器初始化完成')
      // 初始化状态
      volume.value = player.volume || 0.6
      isMuted.value = player.muted || false
    })

    // 监听播放状态
    player.on('play', () => {
      isPlaying.value = true
    })

    player.on('pause', () => {
      isPlaying.value = false
      // 暂停时显示控制栏
      showControls.value = true
    })

    player.on('error', (error: any) => {
      // isReconnecting.value = false;
      console.log('error---------???', error)
    })

    // 监听音量变化
    player.on('volumechange', () => {
      volume.value = player.volume || 0
      isMuted.value = player.muted || false
    })
    //监听播放器获取焦点
    player.on(Events.PLAYER_FOCUS, (data: any) => {
      showControls.value = true;
    })
    player.on(Events.PLAYER_BLUR, (data: any) => {
      if (isPlaying.value) {
        showControls.value = false;
        // 同时隐藏清晰度列表
        showDefinitionList.value = false;
      }
    })
    // 监听全屏状态变化
    player.on('fullscreen_change', (data: any) => {
      isFullscreen.value = data
    })

    // player.on('loadstart', (data: any) => {
    //   isReconnecting.value = true
    // })
    // player.on('loadeddata', (data: any) => {
    //   isReconnecting.value = false
    // })
  }
}
// 检查清晰度是否需要登录
const checkQualityPermission = (qualityName: string) => {
  // 蓝光需要登录
  if (!qualityName) return true; // 如果清晰度名称不存在，默认允许
  if (qualityName === '蓝光' && !isLogin.value) {
    // authStore.showLogin()
    return false
  }
  return true
}

// 手动切换清晰度
const changeDefinition = (item: any) => {
  if (!player) return;

  // 检查权限
  if (!checkQualityPermission(item.definition)) {
    showToast('观看蓝光画质需要登录，请先登录');
    return;
  }

  // 使用播放器的清晰度插件切换清晰度
  const definitionPlugin = player.getPlugin('definition');
  if (definitionPlugin) {
    definitionPlugin.changeDefinition(item);
    currentDefinition.value = item.definition || '高清';
    showDefinitionList.value = false;
  }
}
//监听初始化获取播放地址
watch(() => props.rtmps, (newVal) => {
  console.log(newVal, '---------newVal')
  initPlayer()
}, { immediate: true });
onBeforeUnmount(() => {
  if (player) {
    player.destroy();
    player = null;
  }
});

</script>

<style lang="scss" scoped>
.quality-selector {
  margin-top: 12px;
}

.player-wrap {
  position: relative;
  width: 100%;
  height: 100%;
}

.custom-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  padding: 10px;
  transition: opacity 0.3s;
  z-index: 10;

  &.show {
    opacity: 1;
  }

  &.hide {
    opacity: 0;
    pointer-events: none;
  }

  .controls-area {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .controls-left {
      display: flex;
      align-items: center;

      .quality-btn {
        position: relative;
        color: white;
        background-color: rgba(0, 0, 0, 0.5);
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 10px;
        font-size: 14px;

        .quality-list {
          position: absolute;
          bottom: 100%;
          left: 0;
          background-color: rgba(0, 0, 0, 0.8);
          border-radius: 4px;
          padding: 5px 0;
          margin-bottom: 5px;
          min-width: 100%;

          .quality-item {
            padding: 5px 15px;
            white-space: nowrap;
            cursor: pointer;

            &:hover {
              background-color: rgba(255, 255, 255, 0.1);
            }

            &.active {
              color: #409eff;
            }
          }
        }
      }
    }
  }
}

.reconnecting-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 20;

  .reconnecting-message {
    text-align: center;
    color: white;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top-color: white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 10px;
    }
  }
}

/* 移动端清晰度切换按钮样式 */
.mobile-quality-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 15;
  cursor: pointer;
}

.mobile-quality-list {
  position: absolute;
  top: 45px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 4px;
  padding: 5px 0;
  z-index: 15;
  min-width: 80px;

  .mobile-quality-item {
    padding: 8px 15px;
    color: white;
    text-align: center;
    cursor: pointer;
    font-size: 12px;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    &.active {
      color: #409eff;
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>