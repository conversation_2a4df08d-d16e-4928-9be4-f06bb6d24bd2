<template>
  <div class="rank-list-page">
    <div class="tabs">
      <div v-for="tab in tabs" :key="tab.value" class="tabs-item" :class="{ active: activeTab === tab.value }"
        @click="handleChangeTab(tab.value)">
        {{ tab.label }}
      </div>
    </div>
    <component :is="RankItem" :publicList="publicList" :type="activeTab" />
  </div>
</template>

<script lang="ts" setup>
import RankItem from '../RankItem/index.vue'
import { getPopularityApi, getTicketApi, getContributeApi } from '@/api/match'

const props = defineProps({
  liveId: {
    type: Number,
    default: 0
  }
})

type Tab = {
  label: string
  value: string
}
const tabs: Tab[] = [
  { label: '人气排行榜', value: 'popularity' },
  { label: '球票排行榜', value: 'ticket' },
  { label: '贡献排行榜', value: 'contribute' }
]

const activeTab = ref<string>('popularity') // 默认选中人气排行榜

interface PublicListType {
  grade: number;
  userName: string;
  userImage: string;
  num: number;
  sex: string
}
let publicList = ref<PublicListType[]>([])
let popularityList = ref([])
// 获取人气排行榜
const getPopularity = async () => {
  let res = await getPopularityApi()
  popularityList.value = res.data
  publicList.value = res.data
}

let ticketList = ref([])
// 获取球票排行榜
const getTicket = async () => {
  let res = await getTicketApi()
  ticketList.value = res.data;
}

let contributeList = ref([])
// 获取贡献排行榜
const getContribute = async () => {
  let res = await getContributeApi({ liveId: props.liveId })
  contributeList.value = res.data;
}

// 切换排行榜
const handleChangeTab = (value: string) => {
  activeTab.value = value
  switch (value) {
    case 'popularity':
      publicList.value = popularityList.value
      break
    case 'ticket':
      publicList.value = ticketList.value
      break
    case 'contribute':
      publicList.value = contributeList.value
      break
    default:
      break
  }
}

onMounted(() => {
  getPopularity()
  getTicket()
  getContribute()
})
</script>

<style lang="scss" scoped>
.rank-list-page {
  width: 100%;
  height: auto;
  // height: 240px;
  background: linear-gradient(180deg, #FFFFFF 0%, #FFF6F6 100%);

  .tabs {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: $bf-public-size-16;
    font-size: $bf-font-size-base;
    color: $bf-text-black;
    line-height: $bf-public-size-24;

    .tabs-item {
      padding: $bf-public-size-4 $bf-public-size-10;
      background: #F6F6F6;
    }

    .active {
      background-color: $bf-color-primary;
      color: $bf-text-white;
    }
  }
}
</style>