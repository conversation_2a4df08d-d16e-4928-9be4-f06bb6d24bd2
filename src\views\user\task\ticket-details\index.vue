<template>
  <div class="ticket-details-page">
    <Navbar class="nav-bar" title="球票明细" />
    <div class="ticket-wrapper">
      <div class="my-ticket">我的球票</div>
      <div class="ticket-number">{{ userInfo.ticket }}</div>
    </div>
    <div class="details-list" v-if="ticketData.length > 0">
      <div class="details-item" v-for="item in ticketData" :key="item.id">
        <div class="item-left">
          <p class="title">{{ formatTransType(item.transType) }}</p>
          <p class="date">{{ dayjs(item.createTime).format('M月DD日 HH:mm') }}</p>
        </div>
        <div class="item-value increase" v-if="item.chargeType === 0">+{{ item.chargeMoney }}</div>
        <div class="item-value reduce" v-else>-{{ item.chargeMoney }}</div>
      </div>
    </div>
    <Empty v-else />
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { getTicketDetailsApi } from '@/api/user'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const userInfo = computed(() => userStore.getUserInfo)

interface TicketDataType {
  id: number
  chargeMoney: number
  chargeType: number
  transType: string
  number: number
  createTime: string
}

const formatTransType = (type: string) => {
  switch (type) {
    case 'gift':
      return '送礼物'
    case 'signIn':
      return '完成每日签到'
    case 'viewingDuration':
      return '观看时长到达5分钟'
    case 'dailyFollow':
      return '每日关注'
    case 'dailyBarrage':
      return '每日发送弹幕'
    case 'firstLogin':
      return '首次登录'
    case 'firstReward':
      return '首次打赏'
    case 'InviteFriends':
      return '邀请好友'
    default:
      break
  }
}

const ticketData = ref<TicketDataType[]>([])
// 获取球票明细
const getTicketDetails = async () => {
  const { data } = await getTicketDetailsApi({})
  ticketData.value = data.records
}

onMounted(() => {
  getTicketDetails()
})
</script>

<style lang="scss" scoped>
.ticket-details-page {
  background: $bf-bg-primary;
  height: calc(100vh - 54px);
  overflow-y: hidden;
  color: $bf-text-dark;

  .nav-bar {
    margin-top: 54px;
  }

  .ticket-wrapper {
    margin: $bf-public-size-44 0 $bf-public-size-16;
    padding: $bf-public-size-4 0 $bf-public-size-16;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: $bf-bg-white;
    color: $bf-text-dark;

    .my-ticket {
      font-weight: 600;
      font-size: $bf-font-size-base;
      margin: $bf-public-size-6 0;
    }

    .ticket-number {
      font-weight: 600;
      font-size: $bf-font-size-32;
      margin: $bf-public-size-4 0;
    }
  }

  .details-list {
    height: calc(100vh - 210px);
    overflow-y: auto;

    .details-item {
      background: $bf-bg-white;
      padding: $bf-public-size-6 $bf-public-size-16;
      height: 64px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &:not(:last-child) {
        border-bottom: $bf-public-size-1 solid #ECECEC;
      }

      .item-left {
        .title {
          font-size: $bf-font-size-lg;
          color: $bf-text-dark;
        }

        .date {
          margin-top: $bf-public-size-6;
          font-weight: 600;
          font-size: $bf-font-size-sm;
          color: #AFAFAF;
        }
      }

      .item-value {
        font-weight: 600;
        font-size: $bf-font-size-lg;
      }

      .increase {
        color: $bf-color-primary;
      }

      .reduce {
        color: $bf-status-substitute;
      }
    }
  }
}
</style>