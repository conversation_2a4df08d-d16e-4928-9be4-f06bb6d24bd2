<template>
  <div class="level-page">
    <Navbar class="nav-bar" title="我的等级" />
    <div class="level-banner">
      <!-- 用户信息 -->
      <div class="user-info">
        <img class="user-avatar" :src="userInfo.userImage" alt="用户头像" />
        <section>
          <div class="user-name">{{ userInfo.userName }}</div>
          <div class="user-exp">我的经验{{ userInfo.exp }}</div>
        </section>
      </div>
      <!-- 等级进度条 -->
      <div class="level-progress">
        <van-progress :percentage="percentage" color="#AC7C22" track-color="#F5F5F5" :show-pivot="false"
          stroke-width="8" />
      </div>
      <!-- 等级信息 -->
      <div class="level-info">
        <img class="star-icon" src="@/static/user/star-icon.png" />
        <span class="text">Lv.8</span>
        <span class="text">还需要999经验值达到Lv9</span>
        <span class="text">78%</span>
      </div>
    </div>
    <!-- 如何快速提高等级 -->
    <div class="title">如何快速提高等级</div>
    <div class="level-box">
      <div class="level-item" v-for="item in levelUpOptions" :key="item.label">
        <img class="icon" :src="item.iconUrl" :alt="item.label + '图标'" />
        <section>
          <div class="label">{{ item.label }}</div>
          <div class="desc">{{ item.desc }}</div>
        </section>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/stores/user'
import giftImg from '@/static/user/gift-img.png'
import taskImg from '@/static/user/task-img.png'
import signImg from '@/static/user/sign-img.png'

const userStore = useUserStore()
const userInfo = computed(() => userStore.getUserInfo)

const levelUpOptions = [
  {
    iconUrl: giftImg,
    label: '赠送礼物',
    desc: '礼物刷起来，经验飙到海'
  },
  {
    iconUrl: taskImg,
    label: '每日任务',
    desc: '分享直播间，花式拿经验'
  },
  {
    iconUrl: signImg,
    label: '每日签到',
    desc: '天天签到，轻松得经验'
  }
]

// 进度条进度
const percentage = 70
</script>

<style lang="scss" scoped>
.level-page {
  background: $bf-bg-primary;
  height: calc(100vh - 98px);
  color: $bf-text-dark;

  .nav-bar {
    margin-top: 54px;
  }

  .level-banner {
    height: 176px;
    margin: $bf-public-size-44 $bf-public-size-16 $bf-public-size-16;
    padding: $bf-public-size-16;
    background-image: url('@/static/user/level-banner.png');
    background-size: 100% 100%;
    color: $bf-text-white;

    .user-info {
      display: flex;
      align-items: center;

      .user-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        margin-right: $bf-public-size-32;
      }

      .user-name {
        font-size: $bf-font-size-24;
        line-height: $bf-public-size-24;
        margin-bottom: $bf-public-size-8;
      }

      .user-exp {
        font-weight: 500;
        font-size: $bf-font-size-base;
        line-height: $bf-public-size-24;
      }
    }

    .level-progress {
      margin: $bf-public-size-16 $bf-public-size-17;
    }

    .level-info {
      display: flex;
      align-items: center;
      margin: 0 $bf-public-size-17;

      .star-icon {
        width: $bf-public-size-16;
        height: $bf-public-size-16;
        margin-right: 5px;
      }

      .text {
        font-weight: 500;
        font-size: $bf-font-size-base;
        line-height: $bf-public-size-24;

        &:nth-child(3) {
          margin: 0 $bf-public-size-48 0 $bf-public-size-30;
        }
      }
    }
  }

  .title {
    position: relative;
    text-align: center;
    font-weight: 600;
    font-size: $bf-font-size-lg;
    margin: 0 46px $bf-public-size-16;

    // 使用伪元素创建装饰线
    &::before,
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      width: 88px;
      height: $bf-public-size-1;
      background: linear-gradient(90deg, rgba(115, 115, 115, 0) 0%, $bf-text-gray 100%);
      margin-top: $bf-public-size-1;
    }

    &::before {
      left: 0;
    }

    &::after {
      right: 0;
      background: linear-gradient(90deg, $bf-text-gray 0%, rgba(115, 115, 115, 0) 100%);
    }
  }

  .level-box {
    padding: 0 $bf-public-size-16;
    background: $bf-bg-white;

    .level-item {
      display: flex;
      align-items: center;
      padding: $bf-public-size-14 0;

      .icon {
        width: $bf-public-size-48;
        height: $bf-public-size-48;
        margin-right: $bf-public-size-8;
      }

      .label {
        font-weight: 600;
        font-size: $bf-font-size-lg;
        line-height: $bf-public-size-24;
        margin-bottom: $bf-public-size-8;
      }

      .desc {
        font-size: $bf-font-size-base;
        line-height: $bf-public-size-24;
      }

      &:not(:last-child) {
        border-bottom: $bf-public-size-1 solid #ECECEC;
      }
    }
  }
}
</style>