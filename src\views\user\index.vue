<template>
  <div class="user-page">
    <div class="user-banner"></div>
    <div class="user-top">
      <div class="user-info">
        <img class="user-avatar" :src="userInfo.userImage" alt="用户头像" />
        <div class="user-name">{{ userInfo.userName }}</div>
        <div class="user-uid">UID：{{ userInfo.id }}</div>
      </div>
      <div class="user-grid">
        <div class="grid-item">
          <span>20</span>
          <div class="grid-item name">我的关注</div>
        </div>
        <div class="grid-item vertical-line">
          <span>1</span>
          <div class="grid-item name">我的预约</div>
        </div>
        <div class="grid-item">
          <span>{{ userInfo.exp }}</span>
          <div class="grid-item name">我的经验</div>
        </div>
        <div class="grid-item" @click="handleTicketDetails">
          <span>{{ userInfo.ticket }}</span>
          <div class="grid-item name">我的球票</div>
        </div>
      </div>
    </div>
    <div class="user-bottom">
      <div class="cell" v-for="item in cellOptions" :key="item.title" @click="router.push(item.pathUrl)">
        <img class="cell-icon" :src="item.icon" :alt="item.title" />
        <div class="cell-label">{{ item.title }}</div>
        <img class="cell-right-arrow" src="@/static/user/right-arrow-icon.png" alt="向右箭头">
      </div>
    </div>
    <Tabbar :active="3" />
  </div>
</template>

<script setup lang="ts">
import levelIcon from '@/static/user/level-icon.png'
import proflieIcon from '@/static/user/profile-icon.png'
import taskIcon from '@/static/user/task-icon.png'
import friendsIcon from '@/static/user/firends-icon.png'
import messageIcon from '@/static/user/message-icon.png'
import aboutIcon from '@/static/user/about-icon.png'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()
const userInfo = computed(() => userStore.getUserInfo)

const cellOptions = reactive([
  { icon: levelIcon, title: '我的等级', pathUrl: '/level' },
  { icon: proflieIcon, title: '个人资料', pathUrl: '/profile' },
  { icon: taskIcon, title: '任务中心', pathUrl: '/task' },
  { icon: friendsIcon, title: '邀请好友', pathUrl: '/invite-friends' },
  { icon: messageIcon, title: '意见反馈', pathUrl: '/feedback' },
  { icon: aboutIcon, title: '关于我们', pathUrl: '/about' }
])

// 球票明细
const handleTicketDetails = () => {
  router.push('/ticket-details')
}
</script>

<style lang="scss" scoped>
.user-page {
  height: calc(100vh - 104px);
  overflow-y: auto;
  background: $bf-bg-primary;

  .user-banner {
    width: 100%;
    height: 208px;
    background-image: url('@/static/user/user-banner.png');
    background-size: 100% 100%;
  }

  .user-top {
    height: 209px;
    background: $bf-bg-white;
    border-radius: $bf-public-size-24 $bf-public-size-24 0 0;
    margin: -107px $bf-public-size-16 0;
    color: $bf-text-dark;

    .user-info {
      display: flex;
      flex-direction: column;
      align-items: center;

      .user-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        margin-top: -55px;
      }

      .user-name {
        margin-top: $bf-public-size-2;
        font-size: $bf-font-size-24;
        line-height: $bf-public-size-24;
      }

      .user-uid {
        font-size: $bf-font-size-base;
        line-height: $bf-public-size-24;
        margin-top: $bf-public-size-8;
      }
    }

    .user-grid {
      margin-top: $bf-public-size-16;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-template-rows: auto auto;
      gap: $bf-public-size-8;

      .grid-item {
        font-weight: 500;
        font-size: $bf-font-size-20;
        line-height: $bf-public-size-24;
        text-align: center;
        position: relative;

        &.vertical-line::after {
          content: "";
          position: absolute;
          right: -5px;
          margin-top: -$bf-public-size-40;
          width: $bf-public-size-1;
          height: $bf-public-size-32;
          background-color: $bf-text-undertone;
        }
      }

      .name {
        font-weight: 400;
        font-size: $bf-font-size-base;
        margin-top: $bf-public-size-8;
      }
    }
  }

  .user-bottom {
    margin-top: $bf-public-size-16;
    margin-bottom: 46px;

    .cell {
      display: flex;
      align-items: center;
      background-color: $bf-bg-white;
      padding: $bf-public-size-20 $bf-public-size-16;
      position: relative;

      &:not(:last-child):after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 5%;
        right: 5%;
        height: $bf-public-size-1;
        background: #ECECEC;
      }

      &-icon {
        width: $bf-public-size-24;
        height: $bf-public-size-24;
      }

      &-label {
        font-weight: 600;
        font-size: $bf-font-size-lg;
        color: $bf-text-dark;
        margin-left: $bf-public-size-8;
        flex: 1;
      }

      &-right-arrow {
        width: $bf-public-size-24;
        height: $bf-public-size-24;
      }
    }
  }
}
</style>