import CryptoJS from 'crypto-js'
import { showToast } from 'vant'

export const isEmptyObject = (obj: object): boolean => {
  return Object.keys(obj).length === 0
}

export const getUrlBeforeParms = function (url: string) {
  console.log(url.split('?'), 'urlStrurlStrurlStr')
  // 通过 ? 分割获取后面的参数字符串
  const urlStr = url.split('?')[0]

  if (!urlStr) return {}
  // 创建空对象存储参数
  const obj = {}
  // 再通过 & 将每一个参数单独分割出来
  const paramsArr = urlStr.split('&')
  for (let i = 0, len = paramsArr.length; i < len; i++) {
    // 再通过 = 将每一个参数分割为 key:value 的形式
    const arr = paramsArr[i].split('=')
    obj[arr[0]] = arr[1]
  }
  return obj
}
export const getUrlAfterParms = function (url: string) {
  // 通过 ? 分割获取后面的参数字符串
  const urlStr = url.split('?')[1]

  if (!urlStr) return {}
  // 创建空对象存储参数
  const obj = {}
  // 再通过 & 将每一个参数单独分割出来
  const paramsArr = urlStr.split('&')
  for (let i = 0, len = paramsArr.length; i < len; i++) {
    // 再通过 = 将每一个参数分割为 key:value 的形式
    const arr = paramsArr[i].split('=')
    obj[arr[0]] = arr[1]
  }
  return obj
}

//复制
export const copyText = function (value: string) {
  navigator.clipboard.writeText(value).then(() => {
    showToast('复制成功')
  })
}

export const isMobile = function () {
  const flag = navigator.userAgent.match(
    /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
  )
  return flag
}

// export const debounce = function (func, delay) {
//   let timer
//   return function () {
//     clearTimeout(timer)
//     timer = setTimeout(() => {
//       func.apply(this, arguments)
//     }, delay)
//   }
// }

export const debounce = function <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timer: NodeJS.Timeout

  return function (this: any, ...args: Parameters<T>) {
    clearTimeout(timer)
    timer = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  }
}

export const throttle = function <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timer: NodeJS.Timeout | null = null

  return function (this: any, ...args: Parameters<T>) {
    if (!timer) {
      timer = setTimeout(() => {
        func.apply(this, args)
        timer = null
      }, delay)
    }
  }
}

export const passwordTo32Chars = function (password: string) {
  // 使用 CryptoJS 的 MD5 方法
  const hash = CryptoJS.MD5(password).toString()
  return hash // 将返回32个字符的十六进制字符串
}

export const maskPhoneNumber = function (phone: string) {
  return phone.replace(/(\d{3})(\d{5})(\d{3})/, '$1******$3')
}


//判断奇偶
export function isEven(number) {
  return number % 2 === 0;
}

export function isOdd(number) {
  return number % 2 === 1;
}