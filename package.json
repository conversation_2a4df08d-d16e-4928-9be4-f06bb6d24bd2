{"name": "score-referral-live-h5", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "generate-proto": "pbjs -t static-module -w es6 -o ./src/proto/proto.js ./protos/*.proto && pbts ./src/proto/proto.js -o ./src/proto/proto.d.ts"}, "dependencies": {"amfe-flexible": "^2.2.1", "axios": "^1.10.0", "compressorjs": "^1.2.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "hls.js": "^1.6.7", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "protobufjs": "^7.5.3", "svga-web": "^2.4.1", "vant": "^4.9.20", "vue": "^3.5.17", "vue-router": "^4.5.1", "xgplayer": "^3.0.22", "xgplayer-hls": "^3.0.22"}, "devDependencies": {"@types/node": "^24.0.12", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "postcss-pxtorem": "^6.1.0", "protobufjs-cli": "^1.1.3", "sass": "^1.89.2", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.3", "vue-tsc": "^2.2.12"}}