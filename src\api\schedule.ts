import request from '@/request/request.js'

interface CourseType {
  date: string
  liveTypeEnum: string
}
// 通过比赛类型查询赛程
export const getScheduleListApi = (params: CourseType) => {
  return request.get<any>('/live-customer/foot/getCourse.e', { params })
}

interface collectMatchType {
  matchId: number
  liveType: string
}
// 收藏比赛
export const collectMatchApi = (data: collectMatchType) => {
  return request.post('/live-customer/userCollectMatch/insert', data)
}
// 取消关注比赛
export const cancelCollectMatchApi = (data: collectMatchType) => {
  return request.post('/live-customer/userCollectMatch/cancel', data)
}