<template>
  <Navbar class="nav-bar" title="意见反馈" />
  <div class="feedback-page">
    <div class="feedback-type">
      <div class="title">
        <span class="title-icon">*</span>
        <span>反馈类型：</span>
      </div>
      <div class="tag-container">
        <van-tag v-for="(item, index) in typeOptions" :key="index" :type="item.checked ? 'primary' : 'default'"
          @click="handleChooseType(index)" class="feedback-tag">
          {{ item.name }}
        </van-tag>
      </div>
    </div>
    <div class="additional-notes">
      <div class="title">
        <span class="title-icon">*</span>
        <span>反馈内容：</span>
        <div class="title-desc">请对您遇到的问题进行描述</div>
      </div>
      <van-field v-model="feedbackContent" type="textarea" maxlength="200" placeholder="请输入反馈内容" show-word-limit
        rows="4" class="textarea-field" />
      <div class="title">
        <span>请提供相关问题的截图或图片（{{ fileList.length }}/3）</span>
        <div class="uploader">
          <van-uploader v-model="fileList" multiple :max-count="3" :after-read="afterRead" />
        </div>
      </div>
      <div class="title">
        <text class="title-icon">*</text>
        <text>请输入您的联系方式，以便我们与您联系</text>
      </div>
      <van-field v-model="phoneInput" placeholder="请输入您的联系方式" type="tel" />
    </div>
    <van-button class="submit-btn" @click="handleSubmit">提交反馈</van-button>
  </div>
</template>

<script lang="ts" setup>
import { showToast } from 'vant'
import { uploadFilePathFunH5 } from '@/common/upload'
import { feedbackApi } from '@/api/user'

const router = useRouter()

const typeOptions = reactive([
  { name: '建议', checked: true },
  { name: '错误', checked: false },
  { name: '其他', checked: false }
])

// 选择反馈类型
const handleChooseType = (index: string | number) => {
  typeOptions.forEach(item => {
    item.checked = false
  })
  typeOptions[index].checked = !typeOptions[index].checked
}

const feedbackContent = ref('')
const fileList = ref([])
const phoneInput = ref('')

// 新增图片
const afterRead = async (file: any) => {
  const files = Array.isArray(file) ? file : [file]
  // 使用 Promise.all 正确处理多个异步上传
  const uploadPromises = files.map(async (item) => {
    item.status = 'uploading'
    item.message = '上传中...'
    try {
      const formData = new FormData()
      formData.append('file', item.file)
      const { data } = await uploadFilePathFunH5(formData)
      // 更新文件状态
      item.status = 'done'
      item.message = ''
      item.url = data
      return item
    } catch (error) {
      item.status = 'failed'
      item.message = '上传失败'
      showToast('图片上传失败')
      return item
    }
  })
  // 等待所有上传完成
  await Promise.all(uploadPromises)
}

// 提交反馈
const handleSubmit = async () => {
  const requestData = {
    contentType: '', // 反馈类型
    content: '', // 反馈内容
    contactWay: '', // 联系方式
    contentImages: [] // 问题图片url list 最多三张
  }
  const selectedType = typeOptions.find(item => item.checked)
  // 检查反馈类型
  if (!selectedType) {
    showToast('请选择反馈类型')
    return
  }
  requestData.contentType = selectedType.name
  // 检查反馈内容
  if (!feedbackContent.value.trim()) {
    showToast('请输入反馈内容')
    return
  }
  requestData.content = feedbackContent.value
  // 检查联系方式
  if (!phoneInput.value) {
    showToast('请填写联系方式')
    return
  }
  if (!/^1[3-9]\d{9}$/.test(phoneInput.value)) {
    showToast('请填写正确的手机号')
    return
  }
  requestData.contactWay = phoneInput.value
  // 处理图片
  requestData.contentImages = fileList.value
    .filter(item => item.status === 'done')
    .map(item => item.url)
  try {
    await feedbackApi(requestData)
    showToast('提交反馈成功')
    setTimeout(() => {
      router.push('/user')
    }, 500)
  } catch (error) {
    showToast('提交反馈失败，请稍后重试')
  }
}
</script>

<style lang="scss" scoped>
.nav-bar {
  margin-top: 54px;
}

.feedback-page {
  margin-top: $bf-public-size-44;
  padding: $bf-public-size-8;
  height: calc(100vh - 98px);
  overflow-y: auto;
  color: $bf-text-dark;
  // background-color: $bf-bg-primary;

  .feedback-type {
    // background-color: $bf-bg-white;
    // border-radius: $bf-public-size-16;
    padding: $bf-public-size-8;
    margin-bottom: $bf-public-size-16;

    .title {
      font-size: $bf-font-size-lg;
      margin-bottom: $bf-public-size-16;

      &-icon {
        color: $bf-color-primary;
        margin-right: $bf-public-size-4;
      }
    }

    .tag-container {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      border-color: $bf-color-primary;
      gap: $bf-public-size-16;
      letter-spacing: 1px;

      .feedback-tag {
        display: flex;
        justify-content: center;
        padding: 9px 0;
        border-radius: $bf-public-size-32;
      }

      .van-tag--primary {
        background-color: $bf-color-primary;
      }

      .van-tag--default {
        background-color: $bf-bg-white;
        color: $bf-color-primary;
        border: $bf-public-size-1 solid $bf-color-primary;
        box-sizing: border-box;
      }
    }
  }

  .additional-notes {
    // background-color: $bf-bg-white;
    // border-radius: $bf-public-size-16;
    padding: $bf-public-size-8;
    margin-bottom: $bf-public-size-16;

    .title {
      font-size: $bf-font-size-lg;
      margin-bottom: $bf-public-size-6;

      &-icon {
        color: $bf-color-primary;
        margin-right: $bf-public-size-4;
      }

      .uploader {
        margin-top: $bf-public-size-16;
      }
    }
  }

  .textarea-field {
    background-color: rgba(129, 129, 129, 0.05);
    border-radius: $bf-public-size-8;
    margin-bottom: $bf-public-size-16;

    :deep(.van-field__word-limit) {
      color: rgba(129, 129, 129, 0.5);
      letter-spacing: 1px;
    }
  }

  .title-desc {
    font-size: $bf-font-size-base;
    margin: $bf-public-size-16 0;
  }

  .submit-btn {
    position: absolute;
    left: $bf-public-size-32;
    bottom: 84px;
    width: calc(100% - 64px);
    height: $bf-public-size-50;
    background: $bf-color-primary;
    border-radius: $bf-public-size-24;
    font-weight: 600;
    font-size: $bf-font-size-lg;
    color: $bf-text-white;
    line-height: $bf-public-size-24;
  }
}
</style>