<template>
  <div class="modify-name-page">
    <Navbar class="nav-bar" title="修改性别" rightText="完成" @finish="handleFinish" />
    <div class="sex-box">
      <div class="cell" @click="handleChooseSex('boy')">
        <div class="cell-label">男</div>
        <img class="cell-icon" v-if="sex === 'boy'" src="@/static/user/red-check-icon.png" />
      </div>
      <div class="cell" @click="handleChooseSex('girl')">
        <div class="cell-label">女</div>
        <img class="cell-icon" v-if="sex === 'girl'" src="@/static/user/red-check-icon.png" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { showToast } from 'vant'
import { useUserStore } from '@/stores/user'
import { updateUserSexApi } from '@/api/user'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const sex = ref('')

onMounted(() => {
  sex.value = route.query.sex as string || ''
})

// 选择性别
const handleChooseSex = (value: string) => {
  sex.value = value === 'boy' ? 'boy' : 'girl'
}

const handleFinish = async () => {
  await updateUserSexApi({ sex: sex.value })
  showToast('修改性别成功')
  // 更新store中的用户信息
  userStore.updateUserInfo()
  setTimeout(() => {
    router.back()
  }, 500)
}
</script>

<style lang="scss" scoped>
.modify-name-page {
  height: calc(100vh - 98px);
  background-color: $bf-bg-primary;

  .nav-bar {
    margin-top: 54px;
  }

  .sex-box {
    margin-top: $bf-public-size-44;

    .cell {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: $bf-bg-white;
      height: 64px;
      padding: 0 $bf-public-size-16;

      &:first-child {
        border-bottom: $bf-public-size-1 solid #ECECEC;
      }

      &-label {
        font-size: $bf-font-size-18;
        color: $bf-text-dark;
      }

      &-icon {
        width: $bf-public-size-32;
        height: $bf-public-size-32;
      }
    }
  }
}
</style>