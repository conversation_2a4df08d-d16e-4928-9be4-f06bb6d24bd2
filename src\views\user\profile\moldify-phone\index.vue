<template>
  <div class="modify-phone-page" :style="{ height: (step === 1 ? 'calc(100vh - 98px)' : 'calc(100vh - 114px)') }">
    <Navbar class="nav-bar" title="修改手机" />
    <div class="phone-info" v-if="step === 1">
      <img class="phone-img" src="@/static/user/phone-img.png" />
      <div class="tips">{{ `已绑定手机号码（${maskPhoneNumber(stepOneForm.phone)}）` }}</div>
    </div>
    <!-- 表单区域 -->
    <div class="form-box">
      <template v-if="step === 1">
        <!-- 验证码输入框 -->
        <van-field v-model="stepOneForm.smsCode" placeholder="获取并输入验证码" :error-message="formErrors.smsCode"
          @input="validateCode" @blur="validateCode">
          <template #button>
            <van-count-down v-if="isOneCounting" :time="60 * 1000" format="ss秒可重发" @finish="isOneCounting = false" />
            <span class="get-code" @click="handleGetCode">{{ isOneCounting ? '' : '获取验证码' }}</span>
          </template>
        </van-field>
        <!-- 下一步按钮 -->
        <van-button class="submit-btn" @click="handleNextStep">下一步</van-button>
      </template>
      <template v-else>
        <!-- 手机号输入框 -->
        <van-field class="phone-field" v-model="stepTwoForm.newPhone" placeholder="请输入手机号" type="tel" maxlength="11"
          :error-message="formErrors.phone" @input="validatePhone" @blur="validatePhone" />
        <!-- 验证码输入框 -->
        <van-field v-model="stepTwoForm.newSmsCode" placeholder="获取并输入验证码" :error-message="formErrors.smsCode"
          @input="validateCode" @blur="validateCode">
          <template #button>
            <van-count-down v-if="isTwoCounting" :time="60 * 1000" format="ss秒可重发 " @finish="isTwoCounting = false" />
            <span class="get-code" @click="handleGetNewCode">{{ isTwoCounting ? '' : '获取验证码' }}</span>
          </template>
        </van-field>
        <!-- 确认按钮 -->
        <van-button class="submit-btn" @click="handleSure">确认</van-button>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/stores/user'
import { maskPhoneNumber } from '@/utils/common'
import { showToast } from 'vant'
import { checkCodeApi, getSmsCodeApi, updateUserPhoneApi } from '@/api/user'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 表单数据
const stepOneForm = reactive({ phone: '', smsCode: '' })
const formErrors = reactive({ phone: '', smsCode: '' })
const stepTwoForm = reactive({ newPhone: '', newSmsCode: '' })
const isOneCounting = ref(false)
const isTwoCounting = ref(false)

// 校验验证码
const validateCode = () => {
  if (step.value === 1) {
    if (!stepOneForm.smsCode) {
      formErrors.smsCode = '请输入验证码'
      return false
    }
    if (stepOneForm.smsCode.length < 4 || stepOneForm.smsCode.length > 6) {
      formErrors.smsCode = '验证码长度应为4-6位'
      return false
    }
    formErrors.smsCode = ''
    return true
  } else {
    if (!stepTwoForm.newSmsCode) {
      formErrors.smsCode = '请输入验证码'
      return false
    }
    if (stepTwoForm.newSmsCode.length < 4 || stepTwoForm.newSmsCode.length > 6) {
      formErrors.smsCode = '验证码长度应为4-6位'
      return false
    }
    formErrors.smsCode = ''
    return true
  }
}

// 校验手机号
const validatePhone = () => {
  if (!stepTwoForm.newPhone) {
    formErrors.phone = '请输入手机号'
    return false
  }
  if (!/^1[3-9]\d{9}$/.test(stepTwoForm.newPhone)) {
    formErrors.phone = '请输入正确的手机号'
    return false
  }
  formErrors.phone = ''
  return true
}

// 校验整个表单
const validateForm = () => {
  const isPhoneValid = validatePhone()
  const isCodeValid = validateCode()
  return isPhoneValid && isCodeValid
}

// 获取验证码
const handleGetCode = async () => {
  try {
    await getSmsCodeApi({
      phone: stepOneForm.phone,
      businessType: 'MSG_UPDATE_PHONE'
    })
    isOneCounting.value = true
    showToast('验证码已发送')
  } catch (error) {
    showToast(error.message || '验证码发送失败')
  }
}

// 获取新验证码
const handleGetNewCode = async () => {
  try {
    await getSmsCodeApi({
      phone: stepTwoForm.newPhone,
      businessType: 'MSG_UPDATE_PHONE'
    })
    isTwoCounting.value = true
    showToast('验证码已发送')
  } catch (error) {
    showToast(error.message || '验证码发送失败')
  }
}

// 令牌
const uuid = ref('')
// 步骤
const step = ref(1)
// 下一步
const handleNextStep = async () => {
  if (!validateCode()) return
  const { data } = await checkCodeApi({
    phone: stepOneForm.phone,
    smsCode: stepOneForm.smsCode,
    businessType: 'MSG_UPDATE_PHONE'
  })
  uuid.value = data
  step.value = 2
}

// 确认
const handleSure = async () => {
  if (!validateForm()) return
  await updateUserPhoneApi({
    phone: stepTwoForm.newPhone,
    smsCode: stepTwoForm.newSmsCode,
    uuid: uuid.value
  })
  showToast('修改手机号成功')
  await userStore.loginOut()
  router.push('/')
}

onMounted(() => {
  stepOneForm.phone = route.query.phone as string || ''
})
</script>

<style lang="scss" scoped>
.modify-phone-page {

  .nav-bar {
    margin-top: 54px;
  }

  .phone-info {
    margin-top: $bf-public-size-44;
    display: flex;
    flex-direction: column;
    align-items: center;

    .phone-img {
      margin-top: $bf-public-size-60;
      margin-bottom: $bf-public-size-20;
      width: $bf-public-size-60;
      height: $bf-public-size-60;
    }

    .tips {
      font-size: $bf-font-size-lg;
      color: $bf-text-dark;
      line-height: $bf-public-size-24;
    }
  }

  .form-box {
    margin-top: $bf-public-size-20;
    padding: 0 $bf-public-size-16;

    .van-field {
      padding: $bf-public-size-20 $bf-public-size-16 $bf-public-size-20 0;
    }

    .phone-field {
      margin-top: $bf-public-size-60;
    }

    .get-code,
    .van-count-down {
      color: $bf-color-primary;
    }

    .submit-btn {
      width: calc(100% - 32px);
      margin: $bf-public-size-44 $bf-public-size-16 0;
      border-radius: $bf-public-size-24;
      background: $bf-color-primary;
      border: none;
      color: $bf-text-white;
      height: $bf-public-size-50;
    }
  }
}
</style>