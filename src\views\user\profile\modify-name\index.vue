<template>
  <div class="modify-name-page">
    <Navbar class="nav-bar" title="修改昵称" rightText="完成" @finish="handleFinish" />
    <div class="form">
      <van-field v-model="name" placeholder="请输入昵称">
        <template #right-icon>
          <img class="close-icon" src="@/static/user/close-icon.png" @click="handleClearName" />
        </template>
      </van-field>
    </div>
    <div class="tips">*请输入2-10个字，每30天可修改一次昵称</div>
  </div>
</template>

<script lang="ts" setup>
import { showToast } from 'vant'
import { useUserStore } from '@/stores/user'
import { updateUserNameApi } from '@/api/user'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const name = ref('')

onMounted(() => {
  name.value = route.query.name as string || ''
})
// 清除昵称
const handleClearName = () => {
  name.value = ''
}
const handleFinish = async () => {
  if (name.value === '') {
    showToast('昵称不能为空')
    return
  }
  if (name.value.length < 2 || name.value.length > 10) {
    showToast('昵称应为2-10个字符')
    return
  }
  await updateUserNameApi({ name: name.value })
  showToast('修改昵称成功')
  // 更新store中的用户信息
  userStore.updateUserInfo()
  setTimeout(() => {
    router.back()
  }, 500)
}
</script>

<style lang="scss" scoped>
.modify-name-page {
  height: calc(100vh - 98px);
  background-color: $bf-bg-primary;

  .nav-bar {
    margin-top: 54px;
  }

  .form {
    margin-top: $bf-public-size-44;

    .close-icon {
      width: $bf-public-size-24;
      height: $bf-public-size-24;
    }
  }

  .tips {
    margin: $bf-public-size-16 0 0 $bf-public-size-12;
    font-family: PingFang SC, PingFang SC;
    font-size: $bf-font-size-sm;
    color: $bf-text-gray;
  }
}

:deep(.van-field__control) {
  font-size: $bf-font-size-lg;
  color: $bf-text-dark;
  padding: $bf-public-size-10 0;
}
</style>