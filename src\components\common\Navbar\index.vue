<template>
  <van-nav-bar fixed @click-left="leftClick" :border="border" :style="{ '--nav-height': height + 'px' }">
    <template v-slot:left>
      <img v-if="textColor === 'white'" class="left-arrow-icon" src="@/static/user/white-left-arrow-icon.png"
        alt="返回箭头" />
      <img v-else class="left-arrow-icon" src="@/static/user/left-arrow-icon.png" alt="返回箭头" />
    </template>
    <template v-slot:title>
      <div class="nav-slot-center">
        <span :style="{ color: textColor }">{{ title }}</span>
      </div>
    </template>
    <template v-slot:right>
      <div class="nav-slot-right" @click="rightClick">
        {{ rightText }}
      </div>
    </template>
  </van-nav-bar>
</template>

<script setup>
const router = useRouter()

const props = defineProps({
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 右边文字
  rightText: {
    type: String,
    default: ''
  },
  // 返回上几级页面
  back: {
    type: Number,
    default: 1
  },
  // 是否显示下边框
  border: {
    type: Boolean,
    default: true
  },
  // 文字颜色
  textColor: {
    type: String,
    default: '#333333'
  },
  // 高度
  height: {
    type: Number,
    default: 44
  }
})

const leftClick = () => {
  if (props.back === 1) {
    // 检查浏览器历史记录
    if (window.history.length > 1) {
      router.back()
    } else {
      // 如果没有历史记录，返回首页
      router.push('/')
    }
  } else {
    router.go(-2)
  }
}

// 定义 emits
const emit = defineEmits(['finish'])

const rightClick = () => {
  emit('finish')
}
</script>

<style lang="scss" scoped>
:deep(.van-nav-bar__content) {
  height: var(--nav-height);
  font-family: PingFang SC, PingFang SC;

  .van-nav-bar__left {
    padding: 0;
  }

  .left-arrow-icon {
    width: 36px;
    height: 36px;
  }

  span {
    font-weight: 600;
    font-size: 20px;
  }

  .nav-slot-right {
    font-size: 16px;
    padding: 0 12px;
    color: $bf-color-primary;
  }
}
</style>