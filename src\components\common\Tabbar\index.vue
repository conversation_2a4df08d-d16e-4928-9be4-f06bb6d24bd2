<template>
  <van-tabbar v-model="activeTab" active-color="#FB2B1F" inactive-color="#818181" @change="handleChange">
    <van-tabbar-item v-for="(item, index) in tabbars" :key="item.title">
      <span>{{ item.title }}</span>
      <template #icon="props">
        <img :src="props.active ? tabbars[index].active : tabbars[index].inactive" :alt="item.title" />
      </template>
    </van-tabbar-item>
  </van-tabbar>
</template>

<script lang="ts" setup>
// 导入图片
import liveIcon from '@/static/tabbar/live-icon.png'
import liveIconActive from '@/static/tabbar/live-icon-active.png'
import scheduleIcon from '@/static/tabbar/schedule-icon.png'
import scheduleIconActive from '@/static/tabbar/schedule-icon-active.png'
import followIcon from '@/static/tabbar/follow-icon.png'
import followIconActive from '@/static/tabbar/follow-icon-active.png'
import mineIcon from '@/static/tabbar/mine-icon.png'
import mineIconActive from '@/static/tabbar/mine-icon-active.png'

const router = useRouter()

const props = defineProps({
  active: {
    type: Number,
    default: 0
  }
})

const activeTab = ref(props.active)

const tabbars = [{
  title: '直播',
  active: liveIconActive,
  inactive: liveIcon
},
{
  title: '赛程',
  active: scheduleIconActive,
  inactive: scheduleIcon
},
{
  title: '关注',
  active: followIconActive,
  inactive: followIcon
},
{
  title: '我的',
  active: mineIconActive,
  inactive: mineIcon
}]

const handleChange = (index) => {
  switch (index) {
    case 0:
      router.push('/')
      break
    case 1:
      router.push('/schedule')
      break
    case 2:
      router.push('/follow')
      break
    case 3:
      router.push('/user')
      break
    default:
      break
  }
}
</script>