<template>
  <div class="invite-friends-page">
    <!-- 顶部背景区域 -->
    <div class="friends-banner">
      <!-- 导航栏 -->
      <Navbar class="nav-bar" title="邀请好友" :border="false" textColor="white" :height="68" />
    </div>
    <!-- 我的收益 -->
    <div class="earnings-card">
      <div class="title">我的收益</div>
      <div class="earnings-grid">
        <div class="grid-item vertical-line">
          <div class="label">已邀请好友(人)</div>
          <div class="value">{{ friendsData?.number || 0 }}</div>
        </div>
        <div class="grid-item">
          <div class="label">已获得奖励(球票)</div>
          <div class="value">{{ friendsData?.ticket || 0 }}</div>
        </div>
      </div>
    </div>
    <!-- 邀请好友列表 -->
    <div class="friends-list" v-if="friendsData?.inviteUserLogResPage?.records.length > 0">
      <div class="item" v-for="(item, index) in friendsData?.inviteUserLogResPage?.records" :key="index">
        <div class="user-info">
          <img class="avatar" :src="item.userImage" alt="用户头像" />
          <p class="name">{{ item.userName }}</p>
        </div>
        <p class="date">{{ dayjs(item.createTime).format('YYYY-MM-DD') }}</p>
      </div>
    </div>
    <div class="empty" v-else>
      <p class="empty-text">竟然没有好友</p>
      <p class="empty-text">邀请好友，赚球票，还不快试试</p>
    </div>
    <van-button class="invite-button" @click="handleInviteFriends">立即邀请好友，赚球票大礼</van-button>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { getInviteFriendsListApi } from '@/api/user'

interface RecordType {
  userName: string
  userImage: string
  createTime: string
}
interface InviteUserLogResType {
  records: RecordType[]
}
interface FriendsDataType {
  number: number
  ticket: number
  inviteUserLogResPage: InviteUserLogResType
}

const friendsData = ref<FriendsDataType>()
// 获取邀请好友列表
const getFirendsList = async () => {
  const { data } = await getInviteFriendsListApi({})
  friendsData.value = data || {}
}

// 邀请好友
const handleInviteFriends = () => { }

onMounted(() => {
  getFirendsList()
})
</script>

<style lang="scss" scoped>
.invite-friends-page {
  background: $bf-bg-primary;
  height: calc(100vh - 54px);
  color: $bf-text-dark;
  position: relative;

  /* 顶部背景区域样式 */
  .friends-banner {
    height: 220px;
    background-image: url('@/static/user/friends-banner.png');
    background-size: cover;

    .nav-bar {
      margin-top: 54px;
      background-color: transparent;
    }
  }

  .earnings-card {
    background: linear-gradient(180deg, #FCECED 0%, #FFFFFF 100%), #FFFFFF;
    margin: -136px $bf-public-size-16 0;
    padding: $bf-public-size-32 $bf-public-size-20;
    border-radius: $bf-public-size-16;

    .title {
      font-weight: 600;
      font-size: $bf-font-size-20;
      color: $bf-color-primary;
      display: flex;
      justify-content: center;
      align-items: center;
      line-height: $bf-public-size-28;

      &::before {
        content: '';
        display: inline-block;
        background-image: url('@/static/user/left-line-img.png');
        background-size: cover;
        width: 83.5px;
        height: 8.5px;
        margin-right: $bf-public-size-10;
      }

      &::after {
        content: '';
        display: inline-block;
        background-image: url('@/static/user/right-line-img.png');
        background-size: cover;
        width: 83.5px;
        height: 8.5px;
        margin-left: $bf-public-size-10;
      }
    }

    .earnings-grid {
      margin-top: $bf-public-size-18;
      display: flex;
      justify-content: space-between;

      .grid-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: $bf-public-size-16;
        position: relative;
        width: 114px;

        &.vertical-line::after {
          content: '';
          position: absolute;
          right: -56px;
          margin-top: 23px;
          width: $bf-public-size-1;
          height: $bf-public-size-24;
          background: $bf-text-undertone;
        }

        .label {
          font-size: $bf-font-size-base;
          line-height: $bf-public-size-20;
        }

        .value {
          font-weight: 600;
          font-size: $bf-font-size-24;
          color: $bf-color-primary;
          line-height: 34px;
        }
      }
    }
  }

  .friends-list {
    margin: $bf-public-size-16;
    padding: $bf-public-size-16;
    border-radius: $bf-public-size-16;
    background: $bf-bg-white;
    height: calc(100vh - 350px);
    overflow-y: auto;

    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: $bf-public-size-16;

      &:first-child {
        padding-top: 0;
      }

      .user-info {
        display: flex;
        align-items: center;

        .avatar {
          width: 38px;
          height: 38px;
          border-radius: 50%;
          margin-right: $bf-public-size-8;
        }

        .name {
          font-size: $bf-font-size-base;
        }
      }

      .date {
        font-size: $bf-font-size-base;
      }
    }
  }

  .empty {
    margin-top: 52px;

    &-text {
      text-align: center;
      font-size: $bf-font-size-base;
      opacity: .7;
      line-height: $bf-public-size-20;
    }
  }

  .invite-button {
    position: absolute;
    left: $bf-public-size-32;
    bottom: 97px;
    width: calc(100% - 64px);
    height: $bf-public-size-50;
    background: $bf-color-primary;
    border-radius: $bf-public-size-24;
    font-weight: 600;
    font-size: $bf-font-size-lg;
    color: $bf-text-white;
    line-height: $bf-public-size-24;
  }
}
</style>