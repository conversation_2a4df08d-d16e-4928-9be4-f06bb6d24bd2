.task-page {
  background: $bf-bg-primary;
  height: calc(100vh - 54px);

  /* 顶部背景区域样式 */
  .task-banner {
    height: 270px;
    background-image: url('@/static/user/task-banner.png');
    background-size: cover;

    .nav-bar {
      margin-top: 54px;
      background-color: transparent;
    }

    .user-info {
      display: flex;
      align-items: center;
      color: $bf-text-white;
      padding: 84px $bf-public-size-16 0;

      .user-avatar {
        width: $bf-public-size-60;
        height: $bf-public-size-60;
        border-radius: 50%;
        margin-right: $bf-public-size-8;
      }

      .user-details {
        flex: 1;
        min-width: 0;

        .user-name {
          font-size: $bf-font-size-20;
          line-height: $bf-public-size-24;
          margin-bottom: $bf-public-size-8;
        }

        .user-sign {
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-weight: 500;
          font-size: $bf-font-size-base;
          line-height: $bf-public-size-24;

          .ticket-wrapper {
            display: flex;
            align-items: center;

            .right-arrow-icon {
              width: $bf-public-size-24;
              height: $bf-public-size-24;
              margin-left: $bf-public-size-4;
            }
          }
        }
      }
    }
  }

  .content-scroll {
    height: calc(100vh - 224px);
    overflow-y: auto;
    padding-top: 100px;
    margin-top: -100px;

    /* 签到卡片样式 */
    .sign-card {
      margin: -100px $bf-public-size-16 $bf-public-size-16;
      padding: $bf-public-size-16;
      background: #FFFFFF;
      border-radius: $bf-public-size-24 $bf-public-size-24 $bf-public-size-16 $bf-public-size-16;
      color: $bf-text-dark;

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .header-left,
        .header-right {
          display: flex;
          align-items: center;

          .gift-icon {
            width: $bf-public-size-20;
            height: $bf-public-size-20;
            margin-right: $bf-public-size-4;
          }

          .title {
            font-weight: 600;
            font-size: $bf-font-size-lg;
          }

          .sign-remind {
            font-size: $bf-font-size-sm;
            color: $bf-text-gray;
            margin-right: $bf-public-size-2;
          }
        }
      }

      .desc {
        font-size: $bf-font-size-base;
        line-height: $bf-public-size-24;
        margin: $bf-public-size-6 0 $bf-public-size-12;
      }
    }

    /* 新手奖励卡片样式 */
    .reward-card {
      width: 100%;
      background: $bf-bg-white;
      color: $bf-text-dark;
      padding: $bf-public-size-16;

      .title {
        font-weight: 600;
        font-size: $bf-font-size-18;
        line-height: $bf-public-size-24;
      }

      .reward-box {
        display: flex;
        gap: $bf-public-size-18;

        .card-item {
          flex: 1;
          margin-top: $bf-public-size-16;
          background: #FFF3F2;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: $bf-public-size-8 $bf-public-size-12 $bf-public-size-8 $bf-public-size-8;
          border-radius: $bf-public-size-8;


          .reward-info {

            .reward-title {
              font-weight: 600;
              font-size: $bf-font-size-base;
              color: $bf-color-primary;
              line-height: $bf-public-size-24;
            }

            .ticket {
              display: flex;
              align-items: center;
              margin-top: $bf-public-size-2;

              &-icon {
                width: $bf-public-size-16;
                height: $bf-public-size-16;
                margin-right: $bf-public-size-4;
              }

              &-number {
                font-size: $bf-font-size-sm;
                color: $bf-text-gray;
                line-height: $bf-public-size-24;
              }
            }
          }

          .reward-status {
            width: 64px;
            height: $bf-public-size-28;
            background: #FFFFFF;
            border-radius: $bf-public-size-32;
            font-size: $bf-font-size-sm;
            color: #FB2B1F;
            letter-spacing: $bf-public-size-1;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }
    }

    /* 日常任务卡片样式 */
    .task-card {
      margin: $bf-public-size-16 0;
      padding: $bf-public-size-16;
      width: 100%;
      background: $bf-bg-white;
      color: $bf-text-dark;

      .title {
        font-weight: 600;
        font-size: $bf-font-size-18;
        line-height: $bf-public-size-24;
      }

      .task-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 68px;

        &:not(:last-child) {
          border-bottom: $bf-public-size-1 solid #ECECEC;
        }

        .task-info {
          display: flex;
          align-items: center;

          .task-icon {
            width: $bf-public-size-40;
            height: $bf-public-size-40;
            margin-right: $bf-public-size-8;
          }

          .task-title {
            font-weight: 600;
            font-size: $bf-font-size-lg;
            line-height: $bf-public-size-24;

            span {
              font-size: $bf-font-size-sm;
              color: $bf-color-primary;
            }
          }

          .ticket {
            display: flex;
            align-items: center;
            margin-top: $bf-public-size-4;

            &-text {
              margin-right: $bf-public-size-4;
            }

            &-icon {
              width: $bf-public-size-16;
              height: $bf-public-size-16;
              margin-right: $bf-public-size-4;
            }

            &-number {
              font-size: $bf-font-size-sm;
              color: $bf-text-gray;
              line-height: $bf-public-size-24;
            }
          }
        }

        .task-status {
          width: 64px;
          height: $bf-public-size-28;
          background: $bf-bg-white;
          border-radius: $bf-public-size-32;
          border: $bf-public-size-1 solid $bf-color-primary;
          font-size: $bf-font-size-sm;
          color: $bf-color-primary;
          letter-spacing: $bf-public-size-1;
          display: flex;
          justify-content: center;
          align-items: center;

          &-active {
            color: $bf-text-white;
            background: $bf-color-primary;
          }
        }
      }
    }
  }
}