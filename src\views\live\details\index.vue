<template>
  <div class="details-page">
    <!-- 视频播放器 -->
    <div class="video-box">
      <Player :rtmps="definitionList"></Player>
    </div>
    <!-- 球队比分信息 -->
    <div class="score-box" v-if="liveTypeEnum === 'basket'">
      <div class="team">
        <span class="team-name">{{ liveDetail.awayName }}</span>
        <img class="team-logo" :src="liveDetail.awayLogo" />
      </div>
      <div class="score">
        <span class="num">{{ scoreDetail.awayScore }}</span>
        <span class="num">:</span>
        <span class="num">{{ scoreDetail.homeScore }}</span>
      </div>
      <div class="team away-team">
        <img class="team-logo" :src="liveDetail.homeLogo" />
        <span class="team-name">{{ liveDetail.homeName }}</span>
      </div>
    </div>
    <div class="score-box" v-else>
      <!-- 主队 -->
      <div class="team">
        <span class="team-name">{{ liveDetail.homeName }}</span>
        <img class="team-logo" :src="liveDetail.homeLogo" />
      </div>
      <div class="score">
        <span class="num">{{ scoreDetail.homeScore }}</span>
        <span class="num">:</span>
        <span class="num">{{ scoreDetail.awayScore }}</span>
      </div>
      <!-- 客队 -->
      <div class="team away-team">
        <img class="team-logo" :src="liveDetail.awayLogo" />
        <span class="team-name">{{ liveDetail.awayName }}</span>
      </div>
    </div>
    <div class="anchor-box">
      <!-- 主播信息 -->
      <div class="anchor-info">
        <img class="avatar" :src="liveDetail.userImage" />
        <div>
          <p class="name">{{ liveDetail.userName || '' }}</p>
          <div class="desc">
            <img class="hot-icon" src="@/static/live/red-hot-icon.png" />
            <span class="hot-number">{{ liveDetail.hotNum || 0 }}</span>
            <span class="fans-number">粉丝：{{ liveDetail.collectLiveMax || 0 }}</span>
          </div>
        </div>
      </div>
      <!-- 切换主播 -->
      <div class="anchor-switch">
        <!-- 主播Popover气泡弹出框 -->
        <van-popover v-model:show="showPopover" :actions="actions" @select="handleSelectAnchor">
          <template #reference>
            <span class="switch-text">切换主播：</span>
            <img class="avatar" src="@/static/user/default-avatar.png" v-for="i in 3" />
            <img class="arrow-icon" :src="showPopover ? UpArrowIcon : DownArrowIcon" />
          </template>
        </van-popover>
      </div>
    </div>
    <div class="tabs">
      <!-- Tabs切换 -->
      <van-tabs v-model:active="activeTab" swipeable @change="handleTabChange">
        <van-tab title="聊天" key="chat">
          <Chat ref="chatViewRef" v-if="liveDetail.userId" :roomId="parseInt(liveDetail.userId)" />
        </van-tab>
        <van-tab title="数据" key="data">
          dsadsad adsadsad adsadsad adsadsad adsadsad adsadsad adsadsad adsadsad a
        </van-tab>
        <van-tab title="主播" key="anchor">
          dsadsad adsadsad adsadsad adsadsad adsadsad adsadsad adsadsad adsadsad a
        </van-tab>
        <van-tab title="排行榜" key="rank">
          <RankList :liveId="userId" />
        </van-tab>
        <template #nav-right>
          <div class="follow-box" :class="{ 'followed-box': liveDetail.liveCollectId }" @click="handleFollow">
            <img v-if="!liveDetail.liveCollectId" class="follow-icon" src="@/static/live/follow-icon.png" />
            <p v-if="!liveDetail.liveCollectId" class="follow-text">关注</p>
            <p v-else class="follow-text followed">已关注</p>
          </div>
        </template>
      </van-tabs>

    </div>
    <!-- 聊天页面 -->
    <!-- 数据页面 -->
    <!-- 主播页面 -->
  </div>
</template>

<script lang="ts" setup>
import { showToast } from 'vant'
import { useRoute } from 'vue-router'
import Chat from '@/components/Chat/index.vue'
import UpArrowIcon from '@/static/live/up-arrow-icon.png'
import DownArrowIcon from '@/static/live/down-arrow-icon.png'
import defaultAvatar from '@/static/user/default-avatar.png'
import { getMatchStatApi, getIncidentsApi, getLiveDetailsApi, getScoreDetailsApi } from '@/api/match'
import { followUserApi, cancelFollowUserApi } from '@/api/user'
import { useTaskStore } from '@/stores/task';
const chatViewRef = ref<InstanceType<typeof Chat> | null>(null)

const taskStore = useTaskStore();
const route = useRoute()
const matchId = computed(() => {
  const id = parseInt(route.query.matchId as string)
  return isNaN(id) ? 0 : id
})
const userId = computed(() => {
  const id = parseInt(route.query.userId as string)
  return isNaN(id) ? 0 : id
})
const liveTypeEnum = computed(() => route.query.liveTypeEnum || 'foot')
// 清晰度配置接口
interface MatchLiveClearItem {
  createTime: string;
  id: number;
  liveName: string;
  liveType: string;
  liveUrl: string;
  matchId: number;
  screenshotUrl: string;
}
interface LiveDetail {
  awayLogo: string;
  awayName: string;
  homeLogo: string;
  homeName: string;
  hotMax: string;
  hotNum: number;
  liveCover: string;
  liveTitle: string;
  liveState: number; //0:未开播 1:开始 2：结束
  pullUrl: string;
  sclassName: string;
  userId: string;
  userName: string;
  userImage: string;
  collectLiveMax: number;
  collectId?: string;
  liveNotice?: string;
  awayScore?: number;
  homeScore?: number;
  matchTime?: string;
  liveCollectId?: number
  matchLiveClearList: MatchLiveClearItem[];
}
let liveDetail = reactive<LiveDetail>({
  awayLogo: '',
  awayName: '',
  homeLogo: '',
  homeName: '',
  hotMax: '',
  hotNum: 0,
  liveCover: '',
  liveTitle: '',
  liveState: 1,//主播主动设置开播状态 0:未开播 1:开始 2：结束
  pullUrl: '',
  sclassName: '',
  userId: '',
  userName: '',
  userImage: '',
  collectLiveMax: 0,
  collectId: '',//有值代表已经关注比赛,
  liveNotice: '',//公告-用于直播间提示
  matchLiveClearList: [{
    createTime: "",
    id: 0,
    liveName: "标清",
    liveType: "foot",
    liveUrl: "https://play.rwabwo.com/rrty/hd-zh-1-4249739_bqzm.flv?txSecret=8153b2f17441ed54da6d48d3725da2c9&txTime=686389AA",
    matchId: 0,
    screenshotUrl: ""
  }]
})
//获取带有拉流地址的直播详情
let recursionTime = ref<any>(null)
const isMatchStart = ref(false)

const getLiveDetails = async () => {
  clearTimeout(recursionTime.value)
  try {
    let res = await getLiveDetailsApi({ matchId: matchId.value, userId: userId.value, liveTypeEnum: liveTypeEnum.value as string })
    if (res.data) {
      liveDetail = Object.assign(liveDetail, res.data)
      // 延迟初始化，确保 DOM 已经渲染 判断直播状态1直播中 2 结束
      if (res.data.liveState === 1) {
        nextTick(() => {
          initPlayer()
        });
        //设置全局直播状态
        globaLiveState.value = true;
        intervalLiveState()
      } else if (res.data.liveState === 0) {
        //未开始
        recursionTime.value = setTimeout(() => {
          getLiveDetails()
        }, 30000)
      } else {
        //结束
        globaLiveState.value = false;
        // 清理直播状态定时器
        if (GlobalIntervalLiveState.value) {
          clearInterval(GlobalIntervalLiveState.value)
          GlobalIntervalLiveState.value = null
        }
        // getLivePage()
      }
    }
  } catch (error) {
    console.error('获取直播详情失败:', error)
    // 错误时重置状态
    globaLiveState.value = false;
    // 清理相关定时器
    if (GlobalIntervalLiveState.value) {
      clearInterval(GlobalIntervalLiveState.value)
      GlobalIntervalLiveState.value = null
    }
  }
}
//获取直播状态
const globaLiveState = ref(false)

const getLiveState = async () => {
  try {
    let res = await getLiveDetailsApi({ matchId: matchId.value, userId: userId.value, liveTypeEnum: liveTypeEnum.value as string })
    if (res.data) {
      liveDetail.liveState = res.data.liveState;
      if (res.data.liveState === 3) {
        //直播结束
        if (GlobalIntervalLiveState.value) {
          clearInterval(GlobalIntervalLiveState.value)
          GlobalIntervalLiveState.value = null
        }
        globaLiveState.value = false;
        //销毁播放器
        // destroyPlayer()
      }
    }
  } catch (error) {
    console.error('获取直播状态失败:', error)
    // 错误时也应该停止直播状态检查
    if (GlobalIntervalLiveState.value) {
      clearInterval(GlobalIntervalLiveState.value)
      GlobalIntervalLiveState.value = null
    }
    globaLiveState.value = false;
  }
}
//获取比分详情

interface MatchBaseInfoRes {
  /**
   * 客队logo
   */
  awayLogo?: string;
  /**
   * 客队名称
   */
  awayName?: string;
  /**
   * 客队总分
   */
  awayScore?: number;
  /**
   * 主队logo
   */
  homeLogo?: string;
  /**
   * 主队名称
   */
  homeName?: string;
  /**
   * 主队总分
   */
  homeScore?: number;
  /**
   * 比赛id
   */
  matchId?: number;
  /**
   * 比赛状态
   */
  matchState?: number; //0比赛异常 1未开始 2进行中 3已结束
  /**
   * 比赛时间
   */
  matchTime?: string;
  /**
   * 赛事logo
   */
  sclassLogo?: string;
  /**
   * 联赛名称
   */
  sclassName?: string;
  [property: string]: any;
}
let scoreDetail = reactive<MatchBaseInfoRes>({
  awayScore: 0,
  homeScore: 0,
  matchTime: '',
  matchState: 0,
  sclassName: '',
  awayName: '',
  homeName: '',
  awayLogo: '',
  homeLogo: '',
  sclassLogo: '',
  matchId: 0,
  homeId: 0,
  awayId: 0,
})
const getScoreDetails = async () => {
  try {
    let res = await getScoreDetailsApi({ matchId: matchId.value, liveTypeEnum: liveTypeEnum.value as string })
    if (res.data) {
      if (res.data.matchState === 1 || res.data.matchState === 2) { //1未开始 2进行中
        isMatchStart.value = true;
        intervalDetail()
      } else {
        isMatchStart.value = false;
        //清除定时器
        if (GlobalInterval.value) {
          clearInterval(GlobalInterval.value)
          GlobalInterval.value = null
        }
      }
      scoreDetail = Object.assign(scoreDetail, res.data)
    }
  } catch (error) {
    console.error('获取比分详情失败:', error)
    // 错误时也应该停止定时器和重置状态
    isMatchStart.value = false;
    if (GlobalInterval.value) {
      clearInterval(GlobalInterval.value)
      GlobalInterval.value = null
    }
  }
}
//定时获取视频状态
let GlobalIntervalLiveState = ref<NodeJS.Timeout | null>(null)
const intervalLiveState = () => {
  safeStartInterval(() => {
    getLiveState()
  }, 60000, GlobalIntervalLiveState)
}
//定时刷新数据
let GlobalInterval = ref<NodeJS.Timeout | null>(null)
const intervalDetail = () => {
  safeStartInterval(() => {
    if (liveTypeEnum.value === 'foot') {
      //获取数据
      // getMatchStat()
      //获取事件
      // getIncidents()
    }
    //获取比分
    getScoreDetails()
  }, 60000, GlobalInterval)
}
// 安全启动定时器的函数
const safeStartInterval = (callback: () => void, interval: number, timerRef: Ref<NodeJS.Timeout | null>) => {
  // 先清理现有定时器
  if (timerRef.value) {
    clearInterval(timerRef.value)
    timerRef.value = null
  }
  // 启动新定时器
  timerRef.value = setInterval(callback, interval)
}
// 自定义清晰度切换相关
const definitionList = ref<Array<{ definition: string, url: string, text: string }>>([])
const initPlayer = () => {
  // 准备清晰度配置数据，确保 matchLiveClearList 存在且是数组
  const clearList = Array.isArray(liveDetail.matchLiveClearList) ? liveDetail.matchLiveClearList : [];
  const definitionListData = clearList.map(item => {
    return {
      definition: item.liveName || '标清', // 清晰度标识
      url: item.liveUrl || '',        // 播放地址
      text: item.liveName || '标清'       // 显示文本
    }
  });
  if (definitionListData.length === 0) {
    if (liveDetail.pullUrl) {
      definitionListData.push({
        definition: '标清',
        url: liveDetail.pullUrl,
        text: '标清'
      })
    }

  }
  // 更新自定义清晰度列表
  definitionList.value = definitionListData
}

// 初始化数据的函数
const initData = () => {
  // 确保有有效的 matchId 和 userId 才进行 API 调用
  if (matchId.value > 0 && userId.value > 0) {
    // 获取直播详情
    getLiveDetails()
    // 获取比分详情
    getScoreDetails()

    // 如果是足球类型，获取统计数据和事件数据
    // if (liveTypeEnum.value === 'foot') {
    //   getMatchStat()
    //   getIncidents()
    // }

    //不是足球与篮球是请求热播列表
    // if (liveTypeEnum.value !== 'foot' && liveTypeEnum.value !== 'basket') {
    //   getLivePage()
    // }
  }
}
onMounted(() => {
  // 初始化数据
  initData()
});
// 是否展示主播Popover气泡弹出框
const showPopover = ref(false)

// 通过 actions 属性来定义菜单选项 
const actions = [
  { icon: defaultAvatar, text: '蜡笔建新1' },
  { icon: defaultAvatar, text: '蜡笔建新2' },
  { icon: defaultAvatar, text: '蜡笔建新3' }
]

// 选择主播
const handleSelectAnchor = (action: { text: string }) => {
  showToast(action.text)
}

const activeTab = ref('rank') // 默认选中"聊天"

const tabOptions = reactive([
  { title: '聊天', value: 'chat' },
  { title: '数据', value: 'data' },
  { title: '主播', value: 'anchor' },
  { title: '排行榜', value: 'rank' }
])

// 切换分类标签
const handleTabChange = (name: string) => {
  activeTab.value = name
}

// 是否关注主播
// 关注主播
const handleFollow = () => {
  if (liveDetail.liveCollectId) {
    cancelFollowUserApi({ liveId: userId.value }).then((res: any) => {
      if (res.code === 0) {
        liveDetail.liveCollectId = 0
        liveDetail.collectLiveMax -= 1
      }
    })
  } else {
    followUserApi({ liveId: userId.value }).then((res: any) => {
      if (res.code === 0) {
        //每日任务
        taskStore.dailyFollow()
        liveDetail.liveCollectId = 1
        liveDetail.collectLiveMax += 1
        chatViewRef.value?.followLiveRoom()
      }
    })
  }
}
</script>

<style lang="scss" scoped>
.details-page {
  height: 100svh;
  display: flex;
  flex-direction: column;

  .video-box {
    width: 100%;
    //height: 220px;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%), linear-gradient(180deg, #000000 0%, rgba(0, 0, 0, 0) 100%);
  }

  /* 球队比分信息样式 */
  .score-box {
    width: 100%;
    height: $bf-public-size-24;
    background-image: url('@/static/live/score-bg-img.png');
    background-size: cover;
    font-weight: 600;
    font-size: $bf-font-size-sm;
    color: $bf-text-white;
    display: flex;
    align-items: center;

    .team {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: end;

      &-name {
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &-logo {
        width: $bf-public-size-24;
        height: $bf-public-size-24;
        margin: 0 $bf-public-size-4;
      }
    }

    .away-team {
      display: flex;
      justify-content: start;
    }

    .score {
      width: 80px;
      display: flex;
      justify-content: space-between;
      margin: 0 $bf-public-size-20;
    }
  }

  /* 主播信息样式 */
  .anchor-box {
    background: $bf-bg-primary;
    color: $bf-text-dark;
    display: flex;
    align-items: center;
    padding: 5px $bf-public-size-8;

    .anchor-info {
      display: flex;
      align-items: center;
      flex: 1;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        right: 0;
        margin-top: $bf-public-size-14;
        width: $bf-public-size-2;
        height: $bf-public-size-22;
        background-color: $bf-text-undertone;
      }

      .avatar {
        width: $bf-public-size-50;
        height: $bf-public-size-50;
        border-radius: 50%;
        margin-right: $bf-public-size-8;
      }

      .name {
        font-weight: 600;
        font-size: $bf-font-size-base;
        line-height: $bf-public-size-20;
      }

      .desc {
        display: flex;
        align-items: center;
        line-height: $bf-public-size-24;

        .hot-icon {
          width: $bf-public-size-12;
          height: $bf-public-size-12;
        }

        .hot-number {
          font-size: $bf-font-size-sm;
          color: $bf-color-primary;
          margin-right: $bf-public-size-16;
        }

        .fans-number {
          font-size: $bf-font-size-sm;
        }
      }
    }

    .anchor-switch {
      display: flex;
      align-items: center;
      justify-content: end;
      flex: 1;

      .switch-text {
        font-size: $bf-font-size-base;
        margin-left: $bf-public-size-20;
      }

      .avatar {
        width: $bf-public-size-32;
        height: $bf-public-size-32;
        border-radius: 50%;
        margin-left: -$bf-public-size-12;

        &:first-of-type {
          margin-left: 0;
        }
      }

      .arrow-icon {
        width: $bf-public-size-16;
        height: $bf-public-size-16;
        margin-left: $bf-public-size-6;
      }
    }
  }

  /* tabs 样式  */
  .tabs {
    flex: 1;
    width: 100%;
    overflow: auto;

    .follow-box {
      width: 82px;
      height: 52px;
      background: #FB2B1F;
      display: flex;
      justify-content: center;
      align-items: center;

      .follow-icon {
        width: $bf-public-size-20;
        height: $bf-public-size-20;
        margin-right: $bf-public-size-6;
      }

      .follow-text {
        font-weight: 600;
        font-size: $bf-font-size-lg;
        color: $bf-text-white;
      }

      .followed {
        color: $bf-text-gray;
      }
    }

    .followed-box {
      background: $bf-text-undertone;
    }
  }

}

:deep(.van-tabs) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.van-tabs__content) {
  flex: 1;
  overflow: auto;
}

:deep(.van-tab__panel) {
  height: 100%;
}

/* 主播Popover气泡弹出框样式 */
:deep(.van-popover__content) {
  max-height: 300px;
  overflow-y: auto;
  margin-left: $bf-public-size-10;
  padding-top: $bf-public-size-16;
  border-radius: $bf-public-size-12;
}

:deep(.van-popover__action) {
  width: 156px;
  margin-bottom: $bf-public-size-16;

  &:last-child {
    bottom: 0;
  }
}

:deep(.van-icon__image) {
  width: $bf-public-size-40;
  height: $bf-public-size-40;
}

:deep(.van-popover__action-text) {
  font-size: $bf-font-size-sm;
  color: $bf-text-black;
  line-height: $bf-public-size-14;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  line-height: $bf-public-size-44;
}

/* 调整 tabs 样式 */
:deep(.van-tabs__wrap) {
  height: 52px;

  span {
    font-size: $bf-font-size-lg;
    color: $bf-text-gray;
  }
}

:deep(.van-tab--active) {
  span {
    font-weight: 600;
    color: $bf-color-primary;
  }
}

:deep(.van-tabs__line) {
  background: $bf-color-primary;
  width: $bf-public-size-32;
  height: $bf-public-size-2;
  bottom: $bf-public-size-22;
}
</style>