<template>
  <div class="forget-pwd-page">
    <!-- 关闭按钮 -->
    <div class="close-icon" @click="handleClose">
      <img src="@/static/auth/close-icon.png" alt="关闭按钮">
    </div>
    <!-- logo -->
    <div class="logo">
      <img class="img" src="@/static/auth/logo.png" alt="LOGO" />
    </div>
    <!-- 忘记密码标题 -->
    <div class="forget-pwd-title">忘记密码</div>
    <!-- 表单区域 -->
    <div class="form-box">
      <van-field v-model="forgrtPwdForm.phone" placeholder="请输入手机号" type="tel" maxlength="11"
        :error-message="formErrors.phone" @input="validatePhone" @blur="validatePhone" />
      <van-field v-model="forgrtPwdForm.smsCode" placeholder="获取并输入验证码" :error-message="formErrors.smsCode"
        @input="validateCode" @blur="validateCode">
        <template #button>
          <van-count-down v-if="isCounting" :time="60 * 1000" format="ss秒重新获取" @finish="isCounting = false" />
          <span class="get-code" @click="handleGetCode">{{ isCounting ? '' : '获取验证码' }}</span>
        </template>
      </van-field>
      <van-field v-model="forgrtPwdForm.pwd" placeholder="请输入6-16位新密码" :type="showPassword ? 'text' : 'password'"
        :error-message="formErrors.password" @input="validatePassword" @blur="validatePassword">
        <template #right-icon>
          <van-icon :name="showPassword ? 'eye-o' : 'closed-eye'" @click="handleIsShowPassword" />
        </template>
      </van-field>
      <van-field v-model="forgrtPwdForm.surePassword" placeholder="请再次输入6-16位确认密码"
        :type="showSurePassword ? 'text' : 'password'" :error-message="formErrors.surePassword"
        @input="validateSurePassword" @blur="validateSurePassword">
        <template #right-icon>
          <van-icon :name="showSurePassword ? 'eye-o' : 'closed-eye'" @click="handleIsShowSurePassword" />
        </template>
      </van-field>
      <!-- 确认按钮 -->
      <van-button class="submit-btn" @click="handleSure" :loading="loading">确认</van-button>
    </div>
  </div>
</template>

<script setup>
import { useUserStore } from '@/stores/user'
import { showToast } from 'vant'
import { getSmsCodeApi, updateUserPasswordApi } from '@/api/user'

const router = useRouter()
const userStore = useUserStore()

// 表单数据
const forgrtPwdForm = reactive({ phone: '', smsCode: '', pwd: '', surePassword: '' })
const formErrors = reactive({ phone: '', smsCode: '', password: '', surePassword: '' })
const loading = ref(false)
const isCounting = ref(false)

// 校验手机号
const validatePhone = () => {
  if (!forgrtPwdForm.phone) {
    formErrors.phone = '请输入手机号'
    return false
  }
  if (!/^1[3-9]\d{9}$/.test(forgrtPwdForm.phone)) {
    formErrors.phone = '请输入正确的手机号'
    return false
  }
  formErrors.phone = ''
  return true
}

// 校验验证码
const validateCode = () => {
  if (!forgrtPwdForm.smsCode) {
    formErrors.smsCode = '请输入验证码'
    return false
  }
  if (forgrtPwdForm.smsCode.length < 4 || forgrtPwdForm.smsCode.length > 6) {
    formErrors.smsCode = '验证码长度应为4-6位'
    return false
  }
  formErrors.smsCode = ''
  return true
}

// 校验密码
const validatePassword = () => {
  if (!forgrtPwdForm.pwd) {
    formErrors.password = '请输入密码'
    return false
  }
  if (forgrtPwdForm.pwd.length < 6 || forgrtPwdForm.pwd.length > 16) {
    formErrors.password = '密码长度应为6-16位'
    return false
  }
  formErrors.password = ''
  // 如果确认密码有值，验证是否匹配
  if (forgrtPwdForm.surePassword) {
    validateSurePassword()
  }
  return true
}

// 校验确认密码
const validateSurePassword = () => {
  if (!forgrtPwdForm.surePassword) {
    formErrors.surePassword = '请输入确认密码'
    return false
  }
  if (!forgrtPwdForm.pwd) {
    formErrors.surePassword = '请先输入密码'
    return false
  }
  if (forgrtPwdForm.surePassword !== forgrtPwdForm.pwd) {
    formErrors.surePassword = '确认密码和密码不一致'
    return false
  }
  formErrors.surePassword = ''
  return true
}

// 校验整个表单
const validateForm = () => {
  const isPhoneValid = validatePhone()
  const isCodeValid = validateCode()
  const isPasswordValid = validatePassword()
  const isSurePasswordValid = validateSurePassword()
  return isPhoneValid && isCodeValid && isPasswordValid && isSurePasswordValid
}

// 是否显示密码
const showPassword = ref(false)
// 密码可见性切换
const handleIsShowPassword = () => {
  showPassword.value = !showPassword.value
}
// 是否显示确认密码
const showSurePassword = ref(false)
// 确认密码可见性切换
const handleIsShowSurePassword = () => {
  showSurePassword.value = !showSurePassword.value
}

// 获取验证码
const handleGetCode = async () => {
  if (!validatePhone()) {
    return
  }
  try {
    await getSmsCodeApi({
      phone: forgrtPwdForm.phone,
      businessType: 'MSG_UPDATE_PASSWORD'
    })
    isCounting.value = true
    showToast('验证码已发送')
  } catch (error) {
    showToast(error.message || '验证码发送失败')
  }
}

// 确认
const handleSure = async () => {
  if (!validateForm()) {
    return
  }
  try {
    loading.value = true
    const { data } = await updateUserPasswordApi(forgrtPwdForm)
    console.log('修改密码成功', data)
    userStore.setUserData(data.token, data.userDetail)
    showToast('修改密码成功')
    // 修改密码成功后跳转到登录页
    setTimeout(() => {
      router.push('/login')
    }, 500)
  } catch (error) {
    console.error('修改密码失败', error)
    showToast(error.message || '修改密码失败，请重试')
  } finally {
    loading.value = false
  }
}

// 关闭页面
const handleClose = () => {
  router.back()
}
</script>

<style scoped lang="scss">
.forget-pwd-page {
  width: 100%;
  min-height: 100vh;
  padding: $bf-public-size-32;
  box-sizing: border-box;
  position: relative;

  .close-icon {
    width: $bf-public-size-24;
    height: $bf-public-size-24;
    position: absolute;
    top: $bf-public-size-14;
    left: $bf-public-size-24;
  }

  .logo {
    display: flex;
    justify-content: center;
    margin-top: 72px;

    .img {
      width: 172px;
      height: 42px;
    }
  }

  .forget-pwd-title {
    margin-top: 60px;
    margin-bottom: $bf-public-size-50;
    font-size: $bf-font-size-lg;
    color: $bf-text-dark;
    letter-spacing: $bf-public-size-1;
    text-align: center;
  }

  .form-box {
    margin-top: $bf-public-size-24;

    .van-field {
      padding: $bf-public-size-12 0;
      margin-bottom: $bf-public-size-16;
    }

    .submit-btn {
      width: 100%;
      margin-top: $bf-public-size-40;
      border-radius: $bf-public-size-22;
      background: $bf-color-primary;
      border: none;
      color: white;
      height: $bf-public-size-50;
    }
  }
}
</style>