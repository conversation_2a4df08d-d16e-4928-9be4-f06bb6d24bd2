<template>
  <div class="empty-page">
    <img :src="actualImageSrc" :alt="text" :style="imageStyle" />
    <p class="tips-text" :style="textStyle">{{ text }}</p>
    <van-button class="button" v-if="isShowButton" @click="handleClick">{{ buttonText }}</van-button>
  </div>
</template>

<script lang="ts" setup>

import { computed } from 'vue'
import defaultImage from '@/static/follow/empty-img.png'

// 定义组件属性
const props = defineProps({
  // 提示文字
  text: {
    type: String,
    default: '暂无数据'
  },
  // 图片路径
  imageSrc: {
    type: String,
    default: ''
  },
  // 图片大小
  imageSize: {
    type: [String, Number],
    default: 160
  },
  // 文字颜色
  textColor: {
    type: String,
    default: '#818181'
  },
  // 文字大小
  textSize: {
    type: [String, Number],
    default: 14
  },
  // 是否展示按钮
  isShowButton: {
    type: Boolean,
    default: false
  },
  // 按钮文字
  buttonText: {
    type: String,
    default: '登录'
  }
})

// 计算实际使用的图片路径
const actualImageSrc = computed(() => {
  return props.imageSrc || defaultImage
})

// 计算样式
const imageStyle = computed(() => ({
  width: typeof props.imageSize === 'number' ? `${props.imageSize}px` : props.imageSize,
  height: typeof props.imageSize === 'number' ? `${props.imageSize}px` : props.imageSize
}))

const textStyle = computed(() => ({
  color: props.textColor,
  fontSize: typeof props.textSize === 'number' ? `${props.textSize}px` : props.textSize
}))

const emit = defineEmits(['handleClick'])
const handleClick = () => {
  emit('handleClick')
}
</script>

<style lang="scss" scoped>
.empty-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100vh;
  padding-top: 30px;
  background-color: #FFF;

  .tips-text {
    margin: 0;
    line-height: 1.5;
  }

  .button {
    margin-top: 16px;
    width: 100px;
    height: 42px;
    background: #FB2B1F;
    border-radius: 32px;
    font-weight: 600;
    font-size: 16px;
    color: #FFFFFF;
  }
}
</style>