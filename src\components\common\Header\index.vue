<template>
  <div class="header-wrap" :style="{ backgroundImage: `url(${backgroundImage})` }">
    <van-button v-if="isShowHomeButton" class="button home-btn" @click="router.push('/')">首页</van-button>
    <van-button class="button download-btn" @click="handleDownLoad">下载APP</van-button>
  </div>
</template>

<script lang="ts" setup>
import logoImage from '@/static/live/logo.png'

const router = useRouter()

defineProps({
  // 是否展示首页按钮
  isShowHomeButton: {
    type: Boolean,
    default: false
  },
  // 背景图片URL
  backgroundImage: {
    type: String,
    default: logoImage
  }
})

// 下载APP
const handleDownLoad = () => { }
</script>

<style lang="scss" scoped>
.header-wrap {
  width: 100%;
  height: 54px;
  background-size: 100% 100%;
  position: relative;

  .button {
    height: 32px;
    padding: 0;
    background: #FB2B1F;
    border-radius: 4px;
    font-weight: 600;
    font-size: 12px;
    color: #F2F2F2;
    position: absolute;
    top: 11px;
  }

  .home-btn {
    width: 44px;
    right: 102px;
  }

  .download-btn {
    width: 70px;
    right: 24px;
  }
}
</style>