<template>
  <div class="modify-password-page">
    <Navbar class="nav-bar" title="修改密码" />
    <!-- 表单区域 -->
    <div class="form-box">
      <template v-if="step === 1">
        <!-- 手机号输入框 -->
        <van-field v-model="maskedPhone" disabled />
        <!-- 验证码输入框 -->
        <van-field v-model="stepOneForm.smsCode" placeholder="获取并输入验证码" :error-message="formErrors.smsCode"
          @input="validateCode" @blur="validateCode">
          <template #button>
            <van-count-down v-if="isCounting" :time="60 * 1000" format="ss秒可重发" @finish="isCounting = false" />
            <span class="get-code" @click="handleGetCode">{{ isCounting ? '' : '获取验证码' }}</span>
          </template>
        </van-field>
        <!-- 下一步按钮 -->
        <van-button class="submit-btn" @click="handleNextStep">下一步</van-button>
      </template>
      <template v-else>
        <!-- 密码输入框 -->
        <van-field v-model="stepTwoForm.password" placeholder="请输入6-16位密码" :type="showPassword ? 'text' : 'password'"
          :error-message="formErrors.password" @input="validatePassword" @blur="validatePassword">
          <template #right-icon>
            <van-icon :name="showPassword ? 'eye-o' : 'closed-eye'" @click="handleIsShowPassword" />
          </template>
        </van-field>
        <!-- 确认密码输入框 -->
        <van-field v-model="stepTwoForm.confirmPassword" placeholder="请再次输入6-16位确认密码"
          :type="showSurePassword ? 'text' : 'password'" :error-message="formErrors.confirmPassword"
          @input="validateSurePassword" @blur="validateSurePassword">
          <template #right-icon>
            <van-icon :name="showSurePassword ? 'eye-o' : 'closed-eye'" @click="handleIsShowSurePassword" />
          </template>
        </van-field>
        <!-- 确认按钮 -->
        <van-button class="submit-btn" @click="handleSure">确认</van-button>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/stores/user'
import { maskPhoneNumber } from '@/utils/common'
import { showToast } from 'vant'
import { getSmsCodeApi, updateUserPasswordApi } from '@/api/user'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 格式化手机号
const maskedPhone = computed(() => {
  return maskPhoneNumber(stepOneForm.phone)
})

// 表单数据
const stepOneForm = reactive({ phone: '', smsCode: '' })
const formErrors = reactive({ phone: '', smsCode: '', password: '', confirmPassword: '' })
const stepTwoForm = reactive({ password: '', confirmPassword: '' })
const isCounting = ref(false)

// 校验验证码
const validateCode = () => {
  if (!stepOneForm.smsCode) {
    formErrors.smsCode = '请输入验证码'
    return false
  }
  if (stepOneForm.smsCode.length < 4 || stepOneForm.smsCode.length > 6) {
    formErrors.smsCode = '验证码长度应为4-6位'
    return false
  }
  formErrors.smsCode = ''
  return true
}


// 校验密码
const validatePassword = () => {
  if (!stepTwoForm.password) {
    formErrors.password = '请输入密码'
    return false
  }
  if (stepTwoForm.password.length < 6 || stepTwoForm.password.length > 16) {
    formErrors.password = '密码长度应为6-16位'
    return false
  }
  formErrors.password = ''
  // 如果确认密码有值，验证是否匹配
  if (stepTwoForm.confirmPassword) {
    validateSurePassword()
  }
  return true
}

// 校验确认密码
const validateSurePassword = () => {
  if (!stepTwoForm.confirmPassword) {
    formErrors.confirmPassword = '请输入确认密码'
    return false
  }
  if (!stepTwoForm.password) {
    formErrors.confirmPassword = '请先输入密码'
    return false
  }
  if (stepTwoForm.confirmPassword !== stepTwoForm.password) {
    formErrors.confirmPassword = '确认密码和密码不一致'
    return false
  }
  formErrors.confirmPassword = ''
  return true
}

// 校验整个表单
const validateForm = () => {
  const isPasswordValid = validatePassword()
  const isSurePasswordValid = validateSurePassword()
  return isPasswordValid && isSurePasswordValid
}

// 是否显示密码
const showPassword = ref(false)
// 密码可见性切换
const handleIsShowPassword = () => {
  showPassword.value = !showPassword.value
}
// 是否显示确认密码
const showSurePassword = ref(false)
// 确认密码可见性切换
const handleIsShowSurePassword = () => {
  showSurePassword.value = !showSurePassword.value
}

// 获取验证码
const handleGetCode = async () => {
  try {
    await getSmsCodeApi({
      phone: stepOneForm.phone,
      businessType: 'MSG_UPDATE_PASSWORD'
    })
    isCounting.value = true
    showToast('验证码已发送')
  } catch (error) {
    showToast(error.message || '验证码发送失败')
  }
}

// 步骤
const step = ref(1)
// 下一步
const handleNextStep = async () => {
  if (!validateCode()) return
  step.value = 2
}

// 确认
const handleSure = async () => {
  if (!validateForm()) return
  await updateUserPasswordApi({
    phone: stepOneForm.phone,
    smsCode: stepOneForm.smsCode,
    pwd: stepTwoForm.password
  })
  showToast('修改密码成功')
  // await userStore.loginOut()
  router.push('/')
}

onMounted(() => {
  stepOneForm.phone = route.query.phone as string || ''
})
</script>

<style lang="scss" scoped>
.modify-password-page {
  height: calc(100vh - 114px);

  .nav-bar {
    margin-top: 54px;
  }

  .form-box {
    margin-top: $bf-public-size-60;
    padding: 0 $bf-public-size-16;

    .van-field {
      padding: $bf-public-size-20 $bf-public-size-16 $bf-public-size-20 0;
    }

    .phone-field {
      margin-top: $bf-public-size-60;
    }

    .get-code,
    .van-count-down {
      color: $bf-color-primary;
    }

    .submit-btn {
      width: calc(100% - 32px);
      margin: $bf-public-size-60 $bf-public-size-16 0;
      border-radius: $bf-public-size-24;
      background: $bf-color-primary;
      border: none;
      color: $bf-text-white;
      height: $bf-public-size-50;
    }
  }
}
</style>