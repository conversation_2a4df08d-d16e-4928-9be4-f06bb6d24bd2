<template>
  <div class="demo-container">
    <h2>礼物动画效果演示</h2>
    <div class="controls">
      <button @click="sendGift('玫瑰花', '🌹')" class="gift-btn">送玫瑰花</button>
      <button @click="sendGift('爱心', '❤️')" class="gift-btn">送爱心</button>
      <button @click="sendGift('钻石', '💎')" class="gift-btn">送钻石</button>
      <button @click="sendGift('火箭', '🚀')" class="gift-btn">送火箭</button>
      <button @click="clearAllGifts" class="clear-btn">清空所有</button>
    </div>
    
    <div class="info">
      <p>当前礼物数量: {{ giftShowList.length }}/5</p>
      <p>功能说明:</p>
      <ul>
        <li>3秒内连击同一礼物会累加数量</li>
        <li>超过3秒自动移除礼物</li>
        <li>最多同时显示5条礼物</li>
        <li>超过5条时会移除最旧的礼物</li>
      </ul>
    </div>

    <!-- 礼物动画区域 -->
    <div class="gift-animation-warp">
      <transition-group name="gift-slide" tag="div" class="gift-list">
        <div 
          class="gift-item" 
          v-for="(item, index) in giftShowList" 
          :key="`${item.userId}-${item.url}-${item.timestamp}`"
          :class="{ 'gift-combo': item.giftCount > 1 }"
        >
          <div class="gift-content">
            <span class="username">{{ item.username }}</span>
            <span class="action">送出</span>
            <span class="gift-icon">{{ item.url }}</span>
            <span class="count" v-if="item.giftCount > 1">x{{ item.giftCount }}</span>
          </div>
          <div class="gift-bg"></div>
        </div>
      </transition-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const giftShowList = ref([])
const giftTimers = new Map()

// 添加礼物到动画列表
const addGiftToAnimation = (giftData) => {
  const { userId, username, url, specialUrl, giftCount = 1 } = giftData
  const now = Date.now()
  const timerKey = `${userId}-${url}`
  
  // 查找是否存在相同用户的相同礼物
  const existingIndex = giftShowList.value.findIndex(item => 
    item.userId === userId && item.url === url
  )
  
  if (existingIndex !== -1) {
    // 更新现有礼物的数量和时间戳
    const existingGift = giftShowList.value[existingIndex]
    existingGift.giftCount = (existingGift.giftCount || 1) + giftCount
    existingGift.timestamp = now
    
    // 清除旧的定时器
    if (giftTimers.has(timerKey)) {
      clearTimeout(giftTimers.get(timerKey))
    }
  } else {
    // 检查是否超过最大显示数量（5条）
    if (giftShowList.value.length >= 5) {
      // 移除最旧的礼物
      const oldestGift = giftShowList.value.shift()
      if (oldestGift) {
        const oldTimerKey = `${oldestGift.userId}-${oldestGift.url}`
        if (giftTimers.has(oldTimerKey)) {
          clearTimeout(giftTimers.get(oldTimerKey))
          giftTimers.delete(oldTimerKey)
        }
      }
    }
    
    // 添加新的礼物
    giftShowList.value.push({
      userId,
      username,
      url,
      specialUrl,
      giftCount,
      timestamp: now
    })
  }
  
  // 设置新的定时器，3秒后移除该礼物
  const timer = setTimeout(() => {
    const index = giftShowList.value.findIndex(item => 
      item.userId === userId && item.url === url
    )
    if (index !== -1) {
      giftShowList.value.splice(index, 1)
    }
    giftTimers.delete(timerKey)
  }, 3000)
  
  giftTimers.set(timerKey, timer)
}

// 模拟发送礼物
const sendGift = (giftName: string, giftIcon: string) => {
  const userId = Math.floor(Math.random() * 1000) // 随机用户ID，用于测试连击
  addGiftToAnimation({
    userId: userId,
    username: `用户${userId}`,
    url: giftIcon,
    specialUrl: giftIcon,
    giftCount: 1
  })
}

// 清空所有礼物
const clearAllGifts = () => {
  giftTimers.forEach(timer => clearTimeout(timer))
  giftTimers.clear()
  giftShowList.value = []
}
</script>

<style lang="scss" scoped>
.demo-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.gift-btn {
  padding: 10px 20px;
  background: #FF6B6B;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;

  &:hover {
    background: #FF5252;
  }
}

.clear-btn {
  padding: 10px 20px;
  background: #666;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;

  &:hover {
    background: #555;
  }
}

.info {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;

  ul {
    margin: 10px 0;
    padding-left: 20px;
  }

  li {
    margin: 5px 0;
  }
}

// 礼物动画样式
.gift-animation-warp {
  position: fixed;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  z-index: 1000;
  pointer-events: none;
  width: 100%;
  max-width: 400px;

  .gift-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 0 16px;
  }

  .gift-item {
    position: relative;
    background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%);
    border-radius: 25px;
    padding: 8px 16px;
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
    transform: translateX(-100%);
    animation: slideInGift 0.5s ease-out forwards;
    overflow: hidden;

    &.gift-combo {
      animation: slideInGift 0.5s ease-out forwards, pulseCombo 0.3s ease-in-out 0.5s;
    }

    .gift-content {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      gap: 6px;
      color: white;
      font-size: 14px;
      font-weight: 500;

      .username {
        color: #FFE4B5;
        font-weight: 600;
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .action {
        color: white;
        opacity: 0.9;
      }

      .gift-icon {
        font-size: 20px;
        animation: bounceGift 0.6s ease-in-out infinite alternate;
      }

      .count {
        color: #FFD700;
        font-weight: 700;
        font-size: 16px;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        animation: scaleCount 0.3s ease-out;
      }
    }

    .gift-bg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, 
        rgba(255, 255, 255, 0.1) 0%, 
        rgba(255, 255, 255, 0.05) 50%, 
        rgba(255, 255, 255, 0.1) 100%);
      border-radius: 25px;
      opacity: 0;
      animation: shimmer 2s ease-in-out infinite;
    }
  }
}

// 动画定义
@keyframes slideInGift {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulseCombo {
  0%, 100% {
    transform: translateX(0) scale(1);
  }
  50% {
    transform: translateX(0) scale(1.05);
  }
}

@keyframes bounceGift {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-2px);
  }
}

@keyframes scaleCount {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shimmer {
  0%, 100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

// Vue transition 动画
.gift-slide-enter-active {
  transition: all 0.5s ease-out;
}

.gift-slide-leave-active {
  transition: all 0.3s ease-in;
}

.gift-slide-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.gift-slide-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

.gift-slide-move {
  transition: transform 0.3s ease;
}
</style>
