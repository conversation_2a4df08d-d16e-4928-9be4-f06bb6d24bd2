import { useUserStore } from '@/stores/user.js'
import { useAuthStore } from '@/stores/auth.js'
import axios from 'axios'
import { signRequest } from '@/utils/sign.js'
import { showFailToast } from 'vant'

const request = axios.create({
  baseURL: import.meta.env.VITE_API_URL
})
// request interceptor
request.interceptors.request.use(
  (config: any) => {
    config.data = config.data || {}
    config = signRequest(config)
    return config
  },
  (error) => {
    // do something with request error
    return Promise.reject(error)
  }
)

request.interceptors.response.use(
  async (res) => {
    const userStore = useUserStore()
    const authStore = useAuthStore()

    if (res.data.code === 401) {
      userStore.clearUserData()
      // 使用 Pinia store 显示登录弹窗
      authStore.showLogin()
    } else if (res.data.code !== 0) {
      showFailToast(res.data.message || '系统错误')
    }
    return res.data.code === 0 ? res.data : Promise.reject(res.data)
  },
  (error) => {
    // const msg: string = error.msg || '系统错误！'
    // ElMessage.error(msg)
    console.log('---------这里会执行错误吗')
    return Promise.reject(error.response)
  }
)

export default request
