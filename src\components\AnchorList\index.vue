<template>
  <div class="anchor" v-for="item in anchorList" :key="item.liveId">
    <section>
      <img class="anchor-avatar" :src="item.userImage" :alt="item.userName" />
      <span class="anchor-name">{{ item.userName }}</span>
    </section>
    <section>
      <img class="anchor-icon" src="@/static/follow/heart-icon.png" @click="handleCancelFollowAnchor(item.liveId)" />
      <span class="anchor-status">{{ formatLiveState(item.liveState) }}</span>
    </section>
  </div>
</template>

<script lang="ts" setup>
import { AnchorType } from '@/types'

defineProps({
  // 主播列表数据
  anchorList: {
    type: Array as PropType<AnchorType[]>,
    default: () => []
  }
})

const formatLiveState = (value: number) => {
  switch (value) {
    case 0:
      return '未开始'
    case 1:
      return '直播中'
    default:
      break
  }
}

// 定义 emits
const emit = defineEmits(['handleCancelFollowAnchor'])

// 取消关注主播
const handleCancelFollowAnchor = (liveId: number) => {
  emit('handleCancelFollowAnchor', liveId)
}
</script>

<style lang="scss" scoped>
.anchor {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $bf-public-size-20 $bf-public-size-22 $bf-public-size-20 $bf-public-size-16;
  background: $bf-bg-white;
  margin-bottom: $bf-public-size-8;

  &:last-child {
    margin-bottom: 82px;
  }

  &-avatar {
    width: $bf-public-size-48;
    height: $bf-public-size-48;
    border-radius: 50%;
    margin-right: $bf-public-size-8;
  }

  &-name {
    font-weight: 600;
    font-size: $bf-font-size-lg;
  }

  &-icon {
    width: $bf-public-size-22;
    height: $bf-public-size-20;
    margin-right: $bf-public-size-10;
  }

  &-status {
    font-size: $bf-font-size-base;
  }
}
</style>