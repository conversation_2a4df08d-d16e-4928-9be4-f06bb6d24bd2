// 主播列表类型
export interface AnchorType {
  liveId: number
  liveState: number
  userName: string
  userImage: string
}

// 赛程列表类型
export interface ScheduleType {
  id: number
  matchId: number
  matchTime: string | Date
  sclassName: string
  matchState: number
  collectId?: number
  liveType: string
  liveState: number
  homeLogo: string
  homeName: string
  awayLogo: string
  awayName: string
  homeScore?: string | number
  awayScore?: string | number
  collectMatchDetailResList: Array<{
    id: number
    userId: number
    userName: string
    userImage: string
  }>
  matchDetailRes?: {
    matchId: number
    liveTypeEnum: string
    userId: number
  }
}