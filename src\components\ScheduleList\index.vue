<template>
  <div class="schedule-list" v-for="item in scheduleList" :key="item.id">
    <div class="match-info">
      <div class="event-title">
        <div>{{ dayjs(item.matchTime).format('HH:mm') }}</div>
        <div class="event">{{ item.sclassName }}</div>
      </div>
      <div class="event-status">
        <div class="reserve-info">
          <img src="@/static/schedule/clock-icon-active.png" class="reserve-icon"
            @click="handleCancelFollowSchedule(item.matchId, item.liveType)">
        </div>
        <div class="status-text">{{ formatLiveState(item.liveState) }}</div>
      </div>
    </div>
    <div class="team-info">
      <div>
        <div class="team">
          <img class="team-logo" :src="item.homeLogo" :alt="item.homeName" />
          <span class="team-name">{{ item.homeName }}</span>
        </div>
        <div class="team">
          <img class="team-logo" :src="item.awayLogo" :alt="item.awayName" />
          <span class="team-name">{{ item.awayName }}</span>
        </div>
      </div>
      <div class="score-box" v-if="item.matchState === 2">
        <p>{{ item.homeScore }}</p>
        <p>{{ item.awayScore }}</p>
      </div>
      <div class="anchor-group" v-for="anchor in item.collectMatchDetailResList" :key="anchor.id"
        @click="handleToRoom(item.id, anchor.userId, item)">
        <img class="anchor-avatar" :src="anchor.userImage" :alt="anchor.userName" />
        <div class="anchor-status">LIVE</div>
        <div class="anchor-name">{{ anchor.userName }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { ScheduleType } from '@/types'

defineProps({
  // 赛程列表数据
  scheduleList: {
    type: Array as PropType<ScheduleType[]>,
    default: () => []
  }
})

const formatLiveState = (value: number) => {
  switch (value) {
    case 0:
      return '未开始'
    case 1:
      return '直播中'
    default:
      break
  }
}

// 定义 emits
const emit = defineEmits(['handleToRoom', 'handleFollow'])

// 跳转直播间
const handleToRoom = (matchId: number, userId: number, item: ScheduleType) => {
  emit('handleToRoom', matchId, userId, item)
}

// 取消关注比赛
const handleCancelFollowSchedule = async (matchId: number, liveType: string) => {
  emit('handleFollow', matchId, liveType)
}
</script>

<style lang="scss" scoped>
.schedule-list {
  width: 100%;
  background: $bf-bg-white;
  margin-bottom: $bf-public-size-8;
  padding: $bf-public-size-20 $bf-public-size-16 $bf-public-size-16;

  .match-info {
    display: flex;
    justify-content: space-between;

    .event-title {
      display: flex;
      align-items: center;
      font-size: $bf-font-size-base;

      .event {
        margin-left: $bf-public-size-4;
      }
    }

    .event-status {
      display: flex;
      align-items: center;

      .reserve-info {
        .reserve-icon {
          width: $bf-public-size-20;
          height: $bf-public-size-20;
          margin-right: $bf-public-size-10;
        }
      }

      .status-text {
        font-size: $bf-font-size-base;
      }
    }
  }

  .team-info {
    display: flex;
    align-items: center;
    height: 74px;
    margin-top: $bf-public-size-10;

    .team {
      display: flex;
      align-items: center;

      .team-logo {
        width: $bf-public-size-28;
        height: $bf-public-size-28;
        border-radius: 50%;
      }

      .team-name {
        font-size: $bf-font-size-base;
        margin-left: $bf-public-size-4;
        width: 122px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .team:last-child {
      margin-top: $bf-public-size-12;
    }

    .score-box {
      font-size: $bf-font-size-base;
      color: $bf-status-red;
      width: $bf-public-size-24;
      text-align: center;
      margin-left: $bf-public-size-20;
      margin-right: $bf-public-size-22;

      p:last-child {
        margin-top: $bf-public-size-36;
      }
    }

    .anchor-group {
      width: 52px;
      margin-left: $bf-public-size-4;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;

      .anchor-avatar {
        width: $bf-public-size-40;
        height: $bf-public-size-40;
        border-radius: 50%;
      }

      .anchor-status {
        position: absolute;
        top: $bf-public-size-36;
        width: $bf-public-size-28;
        height: $bf-public-size-14;
        background: $bf-status-red;
        border-radius: $bf-public-size-4;
        font-size: $bf-font-size-8;
        color: $bf-text-white;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .anchor-name {
        font-size: $bf-font-size-sm;
        margin-top: $bf-public-size-10;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 56px;
      }
    }
  }
}
</style>