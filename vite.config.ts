import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from '@vant/auto-import-resolver'
import path from 'path'
// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      imports: ['vue', 'vue-router'],
      dts: true
    }),
    Components({
      resolvers: [VantResolver()],
      dts: true
    }),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@use "@/assets/index.scss" as *; @use "@/assets/variables.scss" as *;`
      }
    },
    postcss: './postcss.config.js', // 指定 PostCSS 配置路径
  },
  server: {
    host: '0.0.0.0', // 允许外部访问
    port: 5173,      // 端口号
    open: false,      // 自动打开浏览器
    cors: true      // 允许跨域
  }
})
