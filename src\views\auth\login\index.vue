<template>
  <div class="login-page">
    <!-- 关闭按钮 -->
    <div class="close-icon" @click="handleClose">
      <img src="@/static/auth/close-icon.png" alt="关闭按钮">
    </div>
    <!-- logo -->
    <div class="logo">
      <img class="img" src="@/static/auth/logo.png" alt="LOGO" />
    </div>
    <!-- 登录标题 -->
    <div class="login-title">登录账号</div>
    <!-- 表单区域 -->
    <div class="form-box">
      <van-field v-model="loginForm.phone" placeholder="请输入手机号" type="tel" maxlength="11"
        :error-message="formErrors.phone" @input="validatePhone" @blur="validatePhone" />
      <van-field v-model="loginForm.password" placeholder="请输入6-16位登录密码" :type="showPassword ? 'text' : 'password'"
        :error-message="formErrors.password" @input="validatePassword" @blur="validatePassword">
        <template #right-icon>
          <van-icon :name="showPassword ? 'eye-o' : 'closed-eye'" @click="handleIsShowPassword" />
        </template>
      </van-field>
      <!-- 协议和忘记密码 -->
      <div class="copy-right">
        <div>
          登录即代表你已同意
          <span class="link-text" @click="onProtocol">《用户服务协议》</span>
        </div>
        <div class="forget-pwd" @click="handleForgetPwd">忘记密码</div>
      </div>
      <!-- 登录按钮 -->
      <van-button class="submit-btn" @click="handleLogin" :loading="loading">登录</van-button>
    </div>
    <!-- 注册新账号 -->
    <div class="register-new-account" @click="handleRegister">注册新账号</div>
  </div>
</template>

<script setup>
import { useUserStore } from '@/stores/user'
import { showToast } from 'vant'
import { loginApi } from '@/api/user'

const router = useRouter()
const userStore = useUserStore()

// 常量定义
const URL_USER_PROTOCOL = 'https://oss.youliaobf.com/prod/static/agreement_user.html'

// 表单数据
const loginForm = reactive({ phone: '', password: '' })
const formErrors = reactive({ phone: '', password: '' })
const loading = ref(false)

// 校验手机号
const validatePhone = () => {
  if (!loginForm.phone) {
    formErrors.phone = '请输入手机号'
    return false
  }
  if (!/^1[3-9]\d{9}$/.test(loginForm.phone)) {
    formErrors.phone = '请输入正确的手机号'
    return false
  }
  formErrors.phone = ''
  return true
}

// 校验密码
const validatePassword = () => {
  if (!loginForm.password) {
    formErrors.password = '请输入密码'
    return false
  }
  if (loginForm.password.length < 6 || loginForm.password.length > 16) {
    formErrors.password = '密码长度应为6-16位'
    return false
  }
  formErrors.password = ''
  return true
}

// 校验整个表单
const validateForm = () => {
  const isPhoneValid = validatePhone()
  const isPasswordValid = validatePassword()
  return isPhoneValid && isPasswordValid
}

// 是否显示密码
const showPassword = ref(false)
// 密码可见性切换
const handleIsShowPassword = () => {
  showPassword.value = !showPassword.value
}

// 登录
const handleLogin = async () => {
  if (!validateForm()) {
    return
  }
  try {
    loading.value = true
    const { data } = await loginApi(loginForm)
    console.log('登录成功', data)
    userStore.setUserData(data.token, data.userDetail)
    showToast('登录成功')
    // 登录成功后跳转到首页
    setTimeout(() => {
      router.push('/')
    }, 500)
  } catch (error) {
    console.error('登录失败', error)
    showToast(error.message || '登录失败，请重试')
  } finally {
    loading.value = false
  }
}

// 注册新账号
const handleRegister = () => {
  router.push('/register')
}

// 跳转忘记密码
const handleForgetPwd = () => {
  router.push('/forget-pwd')
}

// 查看用户协议
const onProtocol = () => {
  window.open(URL_USER_PROTOCOL)
}

// 关闭页面
const handleClose = () => {
  router.back()
}
</script>

<style scoped lang="scss">
.login-page {
  width: 100%;
  min-height: 100vh;
  padding: $bf-public-size-32;
  box-sizing: border-box;
  position: relative;

  .close-icon {
    width: $bf-public-size-24;
    height: $bf-public-size-24;
    position: absolute;
    top: $bf-public-size-14;
    left: $bf-public-size-24;
  }

  .logo {
    display: flex;
    justify-content: center;
    margin-top: 72px;

    .img {
      width: 172px;
      height: 42px;
    }
  }

  .login-title {
    margin-top: 60px;
    margin-bottom: $bf-public-size-50;
    font-size: $bf-font-size-lg;
    color: $bf-text-dark;
    letter-spacing: $bf-public-size-1;
    text-align: center;
  }

  .form-box {
    margin-top: $bf-public-size-24;

    .van-field {
      padding: $bf-public-size-12 0;
      margin-bottom: $bf-public-size-16;
    }

    .copy-right {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: $bf-font-size-sm;
      color: #C2C2C2;

      .link-text {
        color: $bf-color-primary;
      }

      .forget-pwd {
        font-size: $bf-font-size-sm;
        color: $bf-text-gray;
        letter-spacing: $bf-public-size-1;
      }
    }

    .submit-btn {
      width: 100%;
      margin-top: $bf-public-size-40;
      border-radius: $bf-public-size-22;
      background: $bf-color-primary;
      border: none;
      color: $bf-text-white;
      height: $bf-public-size-50;
    }
  }

  .register-new-account {
    margin-top: $bf-public-size-16;
    font-size: $bf-font-size-lg;
    color: $bf-text-dark;
    text-align: center;
    text-decoration-line: underline;
  }
}
</style>