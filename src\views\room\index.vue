<template>
  <div class="player-wrap">
    <Player :rtmps="definitionList"></Player>
  </div>
</template>
<script setup lang="ts">
import { useRoute } from 'vue-router'
import Player from "@/components/player/index.vue";
import { getMatchStatApi, getIncidentsApi, getLiveDetailsApi, getScoreDetailsApi } from '@/api/match'
const route = useRoute()
const matchId = computed(() => {
  const id = parseInt(route.query.matchId as string)
  return isNaN(id) ? 0 : id
})
const userId = computed(() => {
  const id = parseInt(route.query.userId as string)
  return isNaN(id) ? 0 : id
})
const liveTypeEnum = computed(() => route.query.liveTypeEnum || 'foot')
// 清晰度配置接口
interface MatchLiveClearItem {
  createTime: string;
  id: number;
  liveName: string;
  liveType: string;
  liveUrl: string;
  matchId: number;
  screenshotUrl: string;
}
interface LiveDetail {
  awayLogo: string;
  awayName: string;
  homeLogo: string;
  homeName: string;
  hotMax: string;
  hotNum: number;
  liveCover: string;
  liveTitle: string;
  liveState: number; //0:未开播 1:开始 2：结束
  pullUrl: string;
  sclassName: string;
  userId: string;
  userName: string;
  userImage: string;
  collectLiveMax: number;
  collectId?: string;
  liveNotice?: string;
  awayScore?: number;
  homeScore?: number;
  matchTime?: string;
  liveCollectId?: number
  matchLiveClearList: MatchLiveClearItem[];
}
let liveDetail = reactive<LiveDetail>({
  awayLogo: '',
  awayName: '',
  homeLogo: '',
  homeName: '',
  hotMax: '',
  hotNum: 0,
  liveCover: '',
  liveTitle: '',
  liveState: 1,//主播主动设置开播状态 0:未开播 1:开始 2：结束
  pullUrl: '',
  sclassName: '',
  userId: '',
  userName: '',
  userImage: '',
  collectLiveMax: 0,
  collectId: '',//有值代表已经关注比赛,
  liveNotice: '',//公告-用于直播间提示
  matchLiveClearList: [{
    createTime: "",
    id: 0,
    liveName: "标清",
    liveType: "foot",
    liveUrl: "https://play.rwabwo.com/rrty/hd-zh-1-4249739_bqzm.flv?txSecret=8153b2f17441ed54da6d48d3725da2c9&txTime=686389AA",
    matchId: 0,
    screenshotUrl: ""
  }]
})
//获取带有拉流地址的直播详情
let recursionTime = ref<any>(null)
const isMatchStart = ref(false)
const getLiveDetails = async () => {
  clearTimeout(recursionTime.value)
  try {
    let res = await getLiveDetailsApi({ matchId: matchId.value, userId: userId.value, liveTypeEnum: liveTypeEnum.value as string })
    if (res.data) {
      liveDetail = Object.assign(liveDetail, res.data)
      // 延迟初始化，确保 DOM 已经渲染 判断直播状态1直播中 2 结束
      if (res.data.liveState === 1) {
        nextTick(() => {
          initPlayer()
        });
        //设置全局直播状态
        globaLiveState.value = true;
        intervalLiveState()
      } else if (res.data.liveState === 0) {
        //未开始
        recursionTime.value = setTimeout(() => {
          getLiveDetails()
        }, 30000)
      } else {
        //结束
        globaLiveState.value = false;
        // 清理直播状态定时器
        if (GlobalIntervalLiveState.value) {
          clearInterval(GlobalIntervalLiveState.value)
          GlobalIntervalLiveState.value = null
        }
        // getLivePage()
      }
    }
  } catch (error) {
    console.error('获取直播详情失败:', error)
    // 错误时重置状态
    globaLiveState.value = false;
    // 清理相关定时器
    if (GlobalIntervalLiveState.value) {
      clearInterval(GlobalIntervalLiveState.value)
      GlobalIntervalLiveState.value = null
    }
  }
}
//获取直播状态
const globaLiveState = ref(false)
const getLiveState = async () => {
  try {
    let res = await getLiveDetailsApi({ matchId: matchId.value, userId: userId.value, liveTypeEnum: liveTypeEnum.value as string })
    if (res.data) {
      liveDetail.liveState = res.data.liveState;
      if (res.data.liveState === 3) {
        //直播结束
        if (GlobalIntervalLiveState.value) {
          clearInterval(GlobalIntervalLiveState.value)
          GlobalIntervalLiveState.value = null
        }
        globaLiveState.value = false;
        //销毁播放器
        // destroyPlayer()
      }
    }
  } catch (error) {
    console.error('获取直播状态失败:', error)
    // 错误时也应该停止直播状态检查
    if (GlobalIntervalLiveState.value) {
      clearInterval(GlobalIntervalLiveState.value)
      GlobalIntervalLiveState.value = null
    }
    globaLiveState.value = false;
  }
}
//获取比分详情

interface MatchBaseInfoRes {
  /**
   * 客队logo
   */
  awayLogo?: string;
  /**
   * 客队名称
   */
  awayName?: string;
  /**
   * 客队总分
   */
  awayScore?: number;
  /**
   * 主队logo
   */
  homeLogo?: string;
  /**
   * 主队名称
   */
  homeName?: string;
  /**
   * 主队总分
   */
  homeScore?: number;
  /**
   * 比赛id
   */
  matchId?: number;
  /**
   * 比赛状态
   */
  matchState?: number; //0比赛异常 1未开始 2进行中 3已结束
  /**
   * 比赛时间
   */
  matchTime?: string;
  /**
   * 赛事logo
   */
  sclassLogo?: string;
  /**
   * 联赛名称
   */
  sclassName?: string;
  [property: string]: any;
}
let scoreDetail = reactive<MatchBaseInfoRes>({
  awayScore: 0,
  homeScore: 0,
  matchTime: '',
  matchState: 0,
  sclassName: '',
  awayName: '',
  homeName: '',
  awayLogo: '',
  homeLogo: '',
  sclassLogo: '',
  matchId: 0,
  homeId: 0,
  awayId: 0,
})
const getScoreDetails = async () => {
  try {
    let res = await getScoreDetailsApi({ matchId: matchId.value, liveTypeEnum: liveTypeEnum.value as string })
    if (res.data) {
      if (res.data.matchState === 1 || res.data.matchState === 2) { //1未开始 2进行中
        isMatchStart.value = true;
        intervalDetail()
      } else {
        isMatchStart.value = false;
        //清除定时器
        if (GlobalInterval.value) {
          clearInterval(GlobalInterval.value)
          GlobalInterval.value = null
        }
      }
      scoreDetail = Object.assign(scoreDetail, res.data)
    }
  } catch (error) {
    console.error('获取比分详情失败:', error)
    // 错误时也应该停止定时器和重置状态
    isMatchStart.value = false;
    if (GlobalInterval.value) {
      clearInterval(GlobalInterval.value)
      GlobalInterval.value = null
    }
  }
}
//定时获取视频状态
let GlobalIntervalLiveState = ref<NodeJS.Timeout | null>(null)
const intervalLiveState = () => {
  safeStartInterval(() => {
    getLiveState()
  }, 60000, GlobalIntervalLiveState)
}
//定时刷新数据
let GlobalInterval = ref<NodeJS.Timeout | null>(null)
const intervalDetail = () => {
  safeStartInterval(() => {
    if (liveTypeEnum.value === 'foot') {
      //获取数据
      // getMatchStat()
      //获取事件
      // getIncidents()
    }
    //获取比分
    getScoreDetails()
  }, 60000, GlobalInterval)
}
// 安全启动定时器的函数
const safeStartInterval = (callback: () => void, interval: number, timerRef: Ref<NodeJS.Timeout | null>) => {
  // 先清理现有定时器
  if (timerRef.value) {
    clearInterval(timerRef.value)
    timerRef.value = null
  }
  // 启动新定时器
  timerRef.value = setInterval(callback, interval)
}

// 自定义清晰度切换相关
const definitionList = ref<Array<{ definition: string, url: string, text: string }>>([])
// const currentDefinition = ref('高清')
// const showDefinitionList = ref(false)
const initPlayer = () => {

  // 准备清晰度配置数据，确保 matchLiveClearList 存在且是数组
  const clearList = Array.isArray(liveDetail.matchLiveClearList) ? liveDetail.matchLiveClearList : [];
  const definitionListData = clearList.map(item => {
    // let url = item.liveUrl.replace("m3u8", "flv");
    return {
      definition: item.liveName || '标清', // 清晰度标识
      url: item.liveUrl || '',        // 播放地址
      text: item.liveName || '标清'       // 显示文本
    }
  });
  if (definitionListData.length === 0) {
    if (liveDetail.pullUrl) {
      definitionListData.push({
        definition: '标清',
        url: liveDetail.pullUrl,
        text: '标清'
      })
    }

  }
  console.log(definitionListData, '----definitionListData')
  // 更新自定义清晰度列表
  definitionList.value = definitionListData
  // 获取默认播放地址，优先使用高清，否则使用pullUrl
  // let defaultUrl = '';
  // let defaultDefinition = '高清';
  // let defaultIndex = definitionListData.findIndex((item) => {
  //   return item.definition === '高清'
  // })
  // if (defaultIndex !== -1) {
  //   defaultUrl = definitionListData[defaultIndex].url
  //   defaultDefinition = definitionListData[defaultIndex].definition
  // } else {
  //   defaultUrl = liveDetail.pullUrl
  //   if (definitionListData.length > 0) {
  //     defaultDefinition = definitionListData[0].definition
  //   }
  // }

  // 如果没有有效的播放地址，则不初始化播放器
  // if (!defaultUrl) {
  //   console.warn('没有有效的播放地址，无法初始化播放器')
  //   return;
  // }

  // 设置当前清晰度
  // currentDefinition.value = defaultDefinition

}

// 初始化数据的函数
const initData = () => {
  // 确保有有效的 matchId 和 userId 才进行 API 调用
  if (matchId.value > 0 && userId.value > 0) {
    // 获取直播详情
    getLiveDetails()
    // 获取比分详情
    getScoreDetails()

    // 如果是足球类型，获取统计数据和事件数据
    // if (liveTypeEnum.value === 'foot') {
    //   getMatchStat()
    //   getIncidents()
    // }

    //不是足球与篮球是请求热播列表
    // if (liveTypeEnum.value !== 'foot' && liveTypeEnum.value !== 'basket') {
    //   getLivePage()
    // }
  }
}
onMounted(() => {
  // 初始化数据
  initData()
  // 添加点击外部关闭清晰度列表的事件监听
  // document.addEventListener('click', handleClickOutside)
});
</script>
<style lang="scss" scoped></style>