<template>
  <div class="profile-page">
    <Navbar class="nav-bar" title="个人资料" />
    <div class="profile-box">
      <!-- 个人信息 -->
      <section class="user-info">
        <div class="cell">
          <div class="cell-label">修改头像</div>
          <section @click="handleModifyAvatar">
            <img class="cell-avatar" :src="userInfo.userImage" alt="用户头像" />
            <img class="cell-right-arrow" src="@/static/user/right-arrow-icon.png" alt="向右箭头">
          </section>
        </div>
        <div class="cell">
          <div class="cell-label">昵称</div>
          <section @click="handleModifyName">
            <span class="cell-value">{{ userInfo.userName }}</span>
            <img class="cell-right-arrow" src="@/static/user/right-arrow-icon.png" alt="向右箭头">
          </section>
        </div>
        <div class="cell">
          <div class="cell-label">性别</div>
          <section @click="handleModifySex">
            <span class="cell-value">{{ userInfo.sex === 'boy' ? '男' : '女' }}</span>
            <img class="cell-right-arrow" src="@/static/user/right-arrow-icon.png" alt="向右箭头">
          </section>
        </div>
      </section>
      <!-- 修改手机、密码 -->
      <section class="modify-box">
        <div class="cell">
          <div class="cell-label">修改手机</div>
          <section @click="handleModifyPhone">
            <span class="cell-value">{{ maskPhoneNumber(userInfo.phoneNum) }}</span>
            <img class="cell-right-arrow" src="@/static/user/right-arrow-icon.png" alt="向右箭头">
          </section>
        </div>
        <div class="cell">
          <div class="cell-label">修改密码</div>
          <img class="cell-right-arrow" src="@/static/user/right-arrow-icon.png" alt="向右箭头"
            @click="handleModifyPassword">
        </div>
      </section>
    </div>
    <!-- 退出登录按钮 -->
    <van-button class="logout-btn" @click="handleLogout">退出登录</van-button>
  </div>
</template>

<script lang="ts" setup>
import { showToast } from 'vant'
import { useUserStore } from '@/stores/user'
import { uploadImageH5 } from '@/common/upload'
import { maskPhoneNumber } from '@/utils/common'
import { updateHeadImgApi } from '@/api/user'

const router = useRouter()
const userStore = useUserStore()
const userInfo = computed(() => userStore.getUserInfo)

// 修改头像
const handleModifyAvatar = async () => {
  try {
    // 调用上传图片方法，传入ossEnum参数
    const { data }: any = await uploadImageH5('HEAD_IMG')
    if (data) {
      // 调用更新头像Api
      await updateHeadImgApi({ imgUrl: data })
      // 更新用户信息
      await userStore.updateUserInfo()
      showToast('修改头像成功')
    }
  } catch (error) {
    showToast(error || '头像上传失败')
    console.error('头像上传错误:', error)
  }
}

// 修改昵称
const handleModifyName = () => {
  router.push(`/modify-name?name=${userInfo.value.userName}`)
}

// 修改性别
const handleModifySex = () => {
  router.push(`/modify-sex?sex=${userInfo.value.sex}`)
}

// 修改手机
const handleModifyPhone = () => {
  router.push(`/modify-phone?phone=${userInfo.value.phoneNum}`)
}

// 修改密码 
const handleModifyPassword = () => {
  router.push(`/modify-password?phone=${userInfo.value.phoneNum}`)
}

// 退出登录
const handleLogout = async () => {
  try {
    // 调用用户store的退出登录方法
    await userStore.loginOut()
    router.replace('/')
    showToast('退出登录成功')
  } catch (error) {
    showToast(error || '退出登录失败')
  }
}
</script>

<style lang="scss" scoped>
.profile-page {
  height: calc(100vh - 70px);
  background-color: $bf-bg-primary;
  position: relative;

  .nav-bar {
    margin-top: 54px;
  }

  .profile-box {
    margin-top: $bf-public-size-16;
    padding-top: $bf-public-size-44;
    font-size: $bf-font-size-lg;
    color: $bf-text-dark;

    .user-info {

      .cell {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: $bf-bg-white;
        padding: $bf-public-size-20 $bf-public-size-16;
        position: relative;

        &:first-child {
          padding: $bf-public-size-14 $bf-public-size-16;
        }

        &:not(:last-child):after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 5%;
          right: 5%;
          height: $bf-public-size-1;
          background: #ECECEC;
        }

        &-avatar {
          width: 80px;
          height: 80px;
          border-radius: 50%;
        }

        &-value {
          margin-right: $bf-public-size-4;
        }

        &-right-arrow {
          width: $bf-public-size-24;
          height: $bf-public-size-24;
        }
      }
    }

    .modify-box {
      margin-top: $bf-public-size-16;

      .cell {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: $bf-bg-white;
        padding: $bf-public-size-20 $bf-public-size-16;
        position: relative;

        &:not(:last-child):after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 5%;
          right: 5%;
          height: $bf-public-size-1;
          background: #ECECEC;
        }

        &-value {
          margin-right: $bf-public-size-4;
        }

        &-right-arrow {
          width: $bf-public-size-24;
          height: $bf-public-size-24;
        }
      }
    }
  }

  .logout-btn {
    width: calc(100% - 64px);
    border-radius: $bf-public-size-22;
    background: $bf-color-primary;
    border: none;
    color: $bf-text-white;
    height: $bf-public-size-50;
    position: absolute;
    left: $bf-public-size-32;
    bottom: 84px;
  }
}
</style>