<template>
  <div class="follow-page">
    <van-tabs v-model:active="activeTab" swipeable @change="handleTabChange">
      <van-tab v-for="item in tabOptions" :key="item.value" :title="item.title" :name="item.value">
        <!-- 关注主播列表 -->
        <template v-if="activeTab === 'anchor'">
          <div class="anchor-list">
            <!-- <section> -->
            <AnchorList v-if="anchorList.length > 0" :anchorList="anchorList"
              @handleCancelFollowAnchor="handleCancelFollowAnchor" />
            <section v-else>
              <Empty class="empty" v-if="isLogin" text="你还没有关注的主播" />
              <Empty class="empty" v-else text="登录账号关注喜欢的主播" isShowButton="true" @handleClick="router.push('/login')" />
            </section>
            <!-- </section> -->
            <p class="title">为你推荐</p>
            <div class="list-container">
              <van-list v-model:loading="loading" :immediate-check="false" :finished="isShowFinishedText"
                finished-text="没有更多了" @load="onLoad">
                <template v-if="liveData.length > 0">
                  <div class="grid-content">
                    <LiveItem v-for="item in liveData" :key="item.matchId" :live-data="item" />
                  </div>
                </template>
              </van-list>
            </div>
          </div>
        </template>
        <!-- 关注赛程列表 -->
        <template v-else>
          <!-- 日期选择 -->
          <div class="date-nav-bar">
            <div class="date-item" :class="{ active: selectedDate === date.value }" v-for="date in dateList"
              :key="date.value" @click="selectDate(date.value)">
              <div class="date-label">{{ date.label }}</div>
              <div class="date-value">{{ date.value }}</div>
            </div>
          </div>
          <div class="schedule-list">
            <ScheduleList v-if="scheduleList.length > 0" :scheduleList="scheduleList" @handleToRoom="handleToRoom"
              @handleFollow="handleFollow" />
            <section v-else>
              <Empty class="empty" v-if="isLogin" text="你还没有预约的比赛" />
              <Empty class="empty" v-else text="登录账号预约喜欢的比赛 " isShowButton="true"
                @handleClick="router.push('/login')" />
            </section>
            <p class="title">为你推荐</p>
            <div class="list-container">
              <van-list v-model:loading="loading" :immediate-check="false" :finished="isShowFinishedText"
                finished-text="没有更多了" @load="onLoad">
                <template v-if="liveData.length > 0">
                  <div class="grid-content">
                    <LiveItem v-for="item in liveData" :key="item.matchId" :live-data="item" />
                  </div>
                </template>
              </van-list>
            </div>
          </div>
        </template>
      </van-tab>
    </van-tabs>
    <Tabbar :active="2" />
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { showToast } from 'vant'
import { ScheduleType } from '@/types'
import { useUserStore } from '@/stores/user'
import { getLivePageApi } from '@/api/index'
import { cancelCollectMatchApi } from '@/api/schedule'
import { cancelFollowUserApi, getCollectAnchorPageApi, getCollectMatchPageApi } from '@/api/user'

const router = useRouter()
const userStore = useUserStore()
const isLogin = computed(() => userStore.isLogin)

const tabOptions = reactive([
  { title: '主播', value: 'anchor' },
  { title: '比赛', value: 'schedule' }
])

const PAGE_SIZE = 20
const route = useRoute()
const liveData = ref<any[]>([])
const activeTab = ref('anchor') // 默认选中"主播"
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const currentPage = ref(1)
const isRequesting = ref(false) // 请求状态标志

// 获取直播列表
const getLivePage = async (reset = false) => {
  if (isRequesting.value) return // 如果正在请求，则直接返回
  isRequesting.value = true
  try {
    if (reset) {
      liveData.value = []
      currentPage.value = 1
      finished.value = false
    }
    let { data } = await getLivePageApi({ current: currentPage.value, size: PAGE_SIZE })
    liveData.value = reset ? data.records : [...liveData.value, ...data.records]
    finished.value = data.records.length < PAGE_SIZE
    currentPage.value += 1
  } catch (error) {
    console.error('获取直播数据失败:', error)
    liveData.value = []
    finished.value = true
  } finally {
    loading.value = false
    refreshing.value = false
    isRequesting.value = false
  }
}

const onLoad = () => {
  if (finished.value || refreshing.value) {
    finished.value = true
    loading.value = false
    return
  }
  getLivePage()
}

// 切换分类标签
const handleTabChange = (name: string) => {
  activeTab.value = name
}

// 控制是否显示没有更多了
const isShowFinishedText = computed(() => {
  return finished.value && liveData.value.length > 0
})

interface DateItem {
  label: string
  value: string
  formatDate: string
}
// 动态生成日期列表
const generateDateList = () => {
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const today = dayjs()
  const dateList: DateItem[] = []
  for (let i = 0; i < 7; i++) {
    const currentDate = today.add(i, 'day')
    dateList.push({
      label: i === 0 ? '今天' : weekdays[currentDate.day()], // 今天/周几
      value: currentDate.format('MM.DD'), // 格式化为 月.日
      formatDate: currentDate.format('YYYYMMDD') // 接口用 年月日
    })
  }
  return dateList
}
// 初始化日期列表和默认选中日期
const dateList = ref<DateItem[]>(generateDateList())
const selectedDate = ref<string>(dateList.value[0].value)
// 选择日期
const selectDate = (date: string) => {
  selectedDate.value = date
  getScheduleList()
}

// 取消关注主播
const handleCancelFollowAnchor = async (liveId: number) => {
  await cancelFollowUserApi({ liveId })
  showToast('取消关注成功')
  await getAnchorList()
}

// 跳转直播间
const handleToRoom = (matchId: number, userId: number, item: ScheduleType) => {
  let url = ''
  if (item.matchDetailRes) {
    url = `/room?matchId=${item.matchDetailRes.matchId}&userId=${item.matchDetailRes.userId}&liveTypeEnum=${item.matchDetailRes.liveTypeEnum}`
  } else {
    url = `/room?matchId=${matchId}&userId=${userId}&liveTypeEnum=foot`
  }
  router.push(url)
}

// 取消关注比赛
const handleFollow = async (matchId: number, liveType: string) => {
  await cancelCollectMatchApi({ matchId, liveType })
  showToast('取消关注成功')
  await getScheduleList()
}

const anchorList = ref<any[]>([])
// 获取关注主播列表
const getAnchorList = async () => {
  const { data } = await getCollectAnchorPageApi({})
  anchorList.value = data.records
}

const scheduleList = ref<any[]>([])
// 获取赛程列表
const getScheduleList = async () => {
  const selectFormatDate = dateList.value.find(date => date.value === selectedDate.value)?.formatDate
  if (!selectFormatDate) return
  const { data } = await getCollectMatchPageApi({
    current: 1,
    size: 50,
    beginDate: selectFormatDate,
    endDate: selectFormatDate
  })
  scheduleList.value = data.records
}

onMounted(() => {
  const typeFromRoute = route.query.type as string
  if (typeFromRoute && tabOptions.some(item => item.value === typeFromRoute)) {
    activeTab.value = typeFromRoute
  }
  getLivePage()
  getAnchorList()
  getScheduleList()
})
</script>

<style lang="scss" scoped>
.follow-page {
  background-color: $bf-bg-primary;
  color: $bf-text-dark;

  .date-nav-bar {
    height: $bf-public-size-48;
    padding: $bf-public-size-8 0;
    margin: $bf-public-size-16 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 $bf-public-size-16;

    .date-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      background: $bf-bg-white;
      text-align: center;
      min-width: $bf-public-size-40;
      height: $bf-public-size-36;
      font-size: $bf-font-size-10;

      .date-value {
        margin-top: $bf-public-size-4;
      }


      &.active {
        background: $bf-color-primary;
        color: $bf-text-white;
        border-radius: $bf-public-size-2;

        .date-label {
          font-weight: 600;
        }

        .date-value {
          font-weight: 600;
        }
      }
    }
  }

  .anchor-list {
    height: calc(100vh - 154px);
    overflow-y: auto;
  }

  .schedule-list {
    height: calc(100vh - 222px);
    overflow-y: auto;
  }

  .empty {
    height: 310px;
    margin-bottom: $bf-public-size-16;
  }

  .title {
    background-color: $bf-bg-white;
    padding: $bf-public-size-16 $bf-public-size-16 0;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: $bf-font-size-18;
    color: $bf-text-dark;
  }

  .list-container {
    background-color: $bf-bg-white;
    padding: $bf-public-size-16;

    .grid-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: $bf-public-size-10;
    }
  }
}

/* 调整 tabs 样式 */
:deep(.van-tabs__wrap) {
  height: $bf-public-size-50;

  .van-tab {
    flex: none;
    padding: 0;
    padding: 0 $bf-public-size-16;
  }
}

:deep(.van-tabs__line) {
  background: $bf-color-primary;
  width: $bf-public-size-32;
  height: $bf-public-size-2;
  bottom: $bf-public-size-22;
}
</style>