<template>
  <div class="live-page">
    <van-tabs v-model:active="activeTab" swipeable @change="handleTabChange">
      <van-tab v-for="item in tabOptions" :key="item.value" :title="item.title" :name="item.value">
        <p class="title" v-if="activeTab === 'all'">正在热播</p>
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <div class="list-container"
            :style="[{ height: activeTab === 'all' ? 'calc(100vh - 257px)' : 'calc(100vh - 215px)' }, { padding: liveData.length > 0 ? '16px' : '' }]">
            <van-list v-model:loading="loading" :immediate-check="false" :finished="isShowFinishedText"
              finished-text="没有更多了" @load="onLoad">
              <template v-if="liveData.length > 0">
                <div class="grid-content">
                  <LiveItem v-for="item in liveData" :key="item.matchId" :live-data="item" />
                </div>
              </template>
              <Empty class="empty" v-else text="暂无比赛直播" />
            </van-list>
          </div>
        </van-pull-refresh>
      </van-tab>
    </van-tabs>
    <Header :backgroundImage="logoAdImage" />
    <Tabbar :active="0" />
  </div>
</template>

<script setup lang="ts">
import logoAdImage from '@/static/live/logo-ad.png'
import { getLivePageApi, getLivePageByTypeApi } from '@/api/index'

const tabOptions = reactive([
  { title: '全部', value: 'all' },
  { title: '足球', value: 'foot' },
  { title: '篮球', value: 'basket' },
  { title: '英雄联盟', value: 'lol' },
  { title: 'DOTA2', value: 'dota' },
  { title: 'CS:GO', value: 'csgo' },
  { title: '王者荣耀', value: 'hok' }
])

const PAGE_SIZE = 20
const route = useRoute()
const liveData = ref<any[]>([])
const activeTab = ref('all') // 默认选中"全部"
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const currentPage = ref(1)
const isRequesting = ref(false) // 请求状态标志

// 获取直播列表
const getLivePage = async (reset = false) => {
  if (isRequesting.value) return // 如果正在请求，则直接返回
  isRequesting.value = true
  try {
    if (reset) {
      liveData.value = []
      currentPage.value = 1
      finished.value = false
    }
    let { data } = activeTab.value === 'all'
      ? await getLivePageApi({ current: currentPage.value, size: PAGE_SIZE })
      : await getLivePageByTypeApi({ liveTypeEnum: activeTab.value })
    if (activeTab.value === 'all') {
      liveData.value = reset ? data.records : [...liveData.value, ...data.records]
      finished.value = data.records.length < PAGE_SIZE
      currentPage.value += 1
    } else {
      liveData.value = data
      finished.value = true
    }
  } catch (error) {
    console.error('获取直播数据失败:', error)
    liveData.value = []
    finished.value = true
  } finally {
    loading.value = false
    refreshing.value = false
    isRequesting.value = false
  }
}

const onLoad = () => {
  if (activeTab.value !== 'all' || finished.value || refreshing.value) {
    finished.value = true
    loading.value = false
    return
  }
  getLivePage()
}

const onRefresh = () => {
  refreshing.value = true
  finished.value = false
  getLivePage(true)
}

// 切换分类标签
const handleTabChange = (name: string) => {
  activeTab.value = name
  loading.value = true
  getLivePage(true)
}

// 控制是否显示没有更多了
const isShowFinishedText = computed(() => {
  return finished.value && liveData.value.length > 0
})

onMounted(() => {
  const typeFromRoute = route.query.type as string
  if (typeFromRoute && tabOptions.some(item => item.value === typeFromRoute)) {
    activeTab.value = typeFromRoute
  }
  getLivePage()
})
</script>

<style lang="scss" scoped>
.live-page {
  height: calc(100vh - 105px);
  background-color: $bf-bg-primary;

  .title {
    padding: $bf-public-size-16 $bf-public-size-16 0;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: $bf-font-size-18;
    color: $bf-text-dark;
  }

  .list-container {
    // padding: $bf-public-size-16;
    overflow-y: auto;

    .grid-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: $bf-public-size-10;
    }

    .empty {
      height: calc(100vh - 215px);
    }
  }
}

/* 调整 tabs 样式 */
:deep(.van-tabs__wrap) {
  height: $bf-public-size-50;
}

:deep(.van-tabs__line) {
  background: $bf-color-primary;
  width: $bf-public-size-32;
  height: $bf-public-size-2;
  bottom: $bf-public-size-22;
}
</style>