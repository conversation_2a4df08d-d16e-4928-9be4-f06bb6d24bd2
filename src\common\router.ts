import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Live',
    component: () => import('@/views/live/index.vue'),
    meta: { title: '直播' }
  },
  {
    path: '/schedule',
    name: 'Schedule',
    component: () => import('@/views/schedule/index.vue'),
    meta: { title: '赛程' }
  },
  {
    path: '/follow',
    name: 'Follow',
    component: () => import('@/views/follow/index.vue'),
    meta: { title: '关注' }
  },
  {
    path: '/user',
    name: 'User',
    component: () => import('@/views/user/index.vue'),
    meta: { title: '我的' }
  },
  {
    path: '/level',
    name: 'Level',
    component: () => import('@/views/user/level/index.vue'),
    meta: { title: '我的等级' }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/user/profile/index.vue'),
    meta: { title: '个人资料' }
  },
  {
    path: '/task',
    name: 'Task',
    component: () => import('@/views/user/task/index.vue'),
    meta: { title: '任务中心' }
  },
  {
    path: '/invite-friends',
    name: 'InviteFriends',
    component: () => import('@/views/user/friends/index.vue'),
    meta: { title: '邀请好友' }
  },
  {
    path: '/gift-demo',
    name: 'GiftDemo',
    component: () => import('@/views/GiftDemo.vue'),
    meta: { title: '礼物动画演示' }
  },
  {
    path: '/feedback',
    name: 'Feedback',
    component: () => import('@/views/user/feedback/index.vue'),
    meta: { title: '意见反馈' }
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('@/views/user/about/index.vue'),
    meta: { title: '关于我们' }
  },
  {
    path: '/modify-name',
    name: 'ModifyName',
    component: () => import('@/views/user/profile/modify-name/index.vue'),
    meta: { title: '修改昵称' }
  },
  {
    path: '/modify-sex',
    name: 'ModifySex',
    component: () => import('@/views/user/profile/modify-sex/index.vue'),
    meta: { title: '修改性别' }
  },
  {
    path: '/modify-phone',
    name: 'ModifyPhone',
    component: () => import('@/views/user/profile/moldify-phone/index.vue'),
    meta: { title: '修改手机' }
  },
  {
    path: '/modify-password',
    name: 'ModifyPassword',
    component: () => import('@/views/user/profile/modify-password/index.vue'),
    meta: { title: '修改密码' }
  },
  {
    path: '/ticket-details',
    name: 'TicketDetails',
    component: () => import('@/views/user/task/ticket-details/index.vue'),
    meta: { title: '球票明细' }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/login/index.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/register/index.vue'),
    meta: { title: '注册' }
  },
  {
    path: '/room',
    name: 'Room',
    component: () => import('@/views/room/index.vue'),
    meta: { title: '直播间' }
  },
  {
    path: '/forget-pwd',
    name: 'ForgetPwd',
    component: () => import('@/views/auth/forget-pwd/index.vue'),
    meta: { title: '忘记密码' }
  },
  {
    path: '/room',
    name: 'Room',
    component: () => import('@/views/live/details/index.vue'),
    meta: { title: '直播详情' }
  }
]
export default routes
