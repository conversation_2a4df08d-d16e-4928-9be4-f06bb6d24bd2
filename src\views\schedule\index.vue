<template>
  <div class="schedule-page">
    <div class="nav-bar">
      <div class="item" :class="{ active: activeTab === item.value }" v-for="item in tabOptions" :key="item.value"
        @click="handleChangeType(item.value)">
        <span>{{ item.title }}</span>
      </div>
    </div>
    <div class="date-nav-bar">
      <div class="date-box">
        <div class="date-item" :class="{ active: selectedDate === date.value }" v-for="date in dateList"
          :key="date.value" @click="selectDate(date.value)">
          <div class="date-label">{{ date.label }}</div>
          <div class="date-value">{{ date.value }}</div>
        </div>
      </div>
    </div>
    <div v-if="scheduleList.length > 0" class="schedule-list">
      <div class="schedule-box" v-for="item in scheduleList" :key="item.id">
        <div class="match-info">
          <div class="event-title">
            <div>{{ dayjs(item.matchTime).format('HH:mm') }}</div>
            <div class="event">{{ item.sclassName }}</div>
          </div>
          <div class="event-status">
            <div class="reserve-info" v-if="item.matchState !== 2">
              <img v-if="!item?.collectId" src="@/static/schedule/clock-icon.png" class="reserve-icon"
                @click="handleFollow(0, item.id)">
              <img v-else src="@/static/schedule/clock-icon-active.png" class="reserve-icon"
                @click="handleFollow(1, item.id)">
            </div>
            <div class="status-text">{{ formatLiveState(item.liveState) }}</div>
          </div>
        </div>
        <div class="team-info">
          <div>
            <div class="team">
              <img class="team-logo" :src="item.homeLogo" :alt="item.homeName" />
              <span class="team-name">{{ item.homeName }}</span>
            </div>
            <div class="team">
              <img class="team-logo" :src="item.awayLogo" :alt="item.awayName" />
              <span class="team-name">{{ item.awayName }}</span>
            </div>
          </div>
          <div class="score-box" v-if="item.matchState === 2">
            <p>{{ item.homeScore }}</p>
            <p>{{ item.awayScore }}</p>
          </div>
          <div class="anchor-group" v-for="anchor in item.collectMatchDetailResList" :key="anchor.id"
            @click="handleToRoom(item.id, anchor.userId, item)">
            <img class="anchor-avatar" :src="anchor.userImage" :alt="anchor.userName" />
            <div class="anchor-status">LIVE</div>
            <div class="anchor-name">{{ anchor.userName }}</div>
          </div>
        </div>
      </div>
    </div>
    <Empty class="empty" v-else />
    <Tabbar :active="1" />
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { showToast } from 'vant'
import { cancelCollectMatchApi, collectMatchApi, getScheduleListApi } from '@/api/schedule'
import { ScheduleType } from '@/types'

const router = useRouter()

interface tabType {
  title: string
  value: string
}
const tabOptions: tabType[] = [
  { title: '全部', value: 'all' },
  { title: '足球', value: 'foot' },
  { title: '篮球', value: 'basket' },
  { title: '英雄联盟', value: 'lol' },
  { title: 'DOTA2', value: 'dota' },
  { title: 'CS:GO', value: 'csgo' },
  { title: '王者荣耀', value: 'hok' }
]

const formatLiveState = (value: number) => {
  switch (value) {
    case 0:
      return '未开播'
    case 1:
      return '直播中'
    default:
      break
  }
}

interface DateItem {
  label: string
  value: string
  formatDate: string
}
// 动态生成日期列表
const generateDateList = () => {
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const today = dayjs()
  const dateList: DateItem[] = []
  for (let i = 0; i < 7; i++) {
    const currentDate = today.add(i, 'day')
    dateList.push({
      label: i === 0 ? '今天' : weekdays[currentDate.day()], // 今天/周几
      value: currentDate.format('MM.DD'), // 格式化为 月.日
      formatDate: currentDate.format('YYYY-MM-DD') // 接口用 年月日
    })
  }
  return dateList
}
// 初始化日期列表和默认选中日期
const dateList = ref<DateItem[]>(generateDateList())
const selectedDate = ref<string>(dateList.value[0].value)
// 选择日期
const selectDate = (date: string) => {
  selectedDate.value = date
  getScheduleList()
}

let activeTab = ref<string>('foot') // 默认选中"全部"
// 切换分类
const handleChangeType = (value: string) => {
  if (activeTab.value === value) return
  activeTab.value = value
  getScheduleList()
}

const scheduleList = ref<any[]>([])
// 获取赛程列表
const getScheduleList = async () => {
  const selectFormatDate = dateList.value.find(date => date.value === selectedDate.value)?.formatDate
  if (!selectFormatDate) return
  const { data } = await getScheduleListApi({
    date: selectFormatDate,
    liveTypeEnum: activeTab.value
  })
  scheduleList.value = data
}

onMounted(() => {
  getScheduleList()
})

// 跳转直播间
const handleToRoom = (matchId: number, userId: number, item: ScheduleType) => {
  let url = ''
  if (item.matchDetailRes) {
    url = `/room?matchId=${item.matchDetailRes.matchId}&userId=${item.matchDetailRes.userId}&liveTypeEnum=${item.matchDetailRes.liveTypeEnum}`
  } else {
    url = `/room?matchId=${matchId}&userId=${userId}&liveTypeEnum=${activeTab.value}`
  }
  router.push(url)
}

// 关注、取消关注比赛
const handleFollow = async (type: number, matchId: number) => {
  let data = {
    matchId,
    liveType: activeTab.value
  }
  type ? await cancelCollectMatchApi(data) : await collectMatchApi(data)
  type ? showToast('取消关注成功') : showToast('关注成功')
  await getScheduleList()
}
</script>

<style lang="scss" scoped>
.schedule-page {
  height: calc(100vh - 105px);
  background-color: $bf-bg-primary;
  color: $bf-text-dark;

  .nav-bar {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $bf-public-size-16;
    font-size: $bf-font-size-base;
    color: $bf-text-dark;
    background-color: $bf-bg-white;
    overflow-x: auto;

    .item {
      position: relative;
    }

    .active {

      &::after {
        content: "";
        position: absolute;
        bottom: -7px;
        left: 50%;
        transform: translateX(-50%);
        width: $bf-public-size-26;
        height: $bf-public-size-2;
        background: $bf-color-primary;
      }
    }
  }

  .date-nav-bar {
    height: $bf-public-size-48;
    padding: $bf-public-size-8 0;
    margin: $bf-public-size-16 0;

    .date-box {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-between;
      padding: 0 $bf-public-size-16;

      .date-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        background: $bf-bg-white;
        text-align: center;
        min-width: $bf-public-size-40;
        height: $bf-public-size-36;
        font-size: $bf-font-size-10;

        .date-value {
          margin-top: $bf-public-size-4;
        }


        &.active {
          background: $bf-color-primary;
          color: $bf-text-white;
          border-radius: $bf-public-size-2;

          .date-label {
            font-weight: 600;
          }

          .date-value {
            font-weight: 600;
          }
        }
      }
    }
  }

  .schedule-list {
    height: calc(100vh - 236px);
    overflow-y: auto;

    .schedule-box {
      width: 100%;
      background: $bf-bg-white;
      margin-bottom: $bf-public-size-8;
      padding: $bf-public-size-20 $bf-public-size-16 $bf-public-size-16;

      .match-info {
        display: flex;
        justify-content: space-between;

        .event-title {
          display: flex;
          align-items: center;
          font-size: $bf-font-size-base;

          .event {
            margin-left: $bf-public-size-4;
          }
        }

        .event-status {
          display: flex;
          align-items: center;

          .reserve-info {
            .reserve-icon {
              width: $bf-public-size-20;
              height: $bf-public-size-20;
              margin-right: $bf-public-size-10;
            }
          }

          .status-text {
            font-size: $bf-font-size-base;
          }
        }
      }

      .team-info {
        display: flex;
        align-items: center;
        height: 74px;
        margin-top: $bf-public-size-10;

        .team {
          display: flex;
          align-items: center;

          .team-logo {
            width: $bf-public-size-28;
            height: $bf-public-size-28;
            border-radius: 50%;
          }

          .team-name {
            font-size: $bf-font-size-base;
            margin-left: $bf-public-size-4;
            width: 122px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .team:last-child {
          margin-top: $bf-public-size-12;
        }

        .score-box {
          font-size: $bf-font-size-base;
          color: $bf-status-red;
          width: $bf-public-size-24;
          text-align: center;
          margin-left: $bf-public-size-20;
          margin-right: $bf-public-size-22;

          p:last-child {
            margin-top: $bf-public-size-36;
          }
        }

        .anchor-group {
          width: 52px;
          margin-left: $bf-public-size-4;
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;

          .anchor-avatar {
            width: $bf-public-size-40;
            height: $bf-public-size-40;
            border-radius: 50%;
          }

          .anchor-status {
            position: absolute;
            top: $bf-public-size-36;
            width: $bf-public-size-28;
            height: $bf-public-size-14;
            background: $bf-status-red;
            border-radius: $bf-public-size-4;
            font-size: $bf-font-size-8;
            color: $bf-text-white;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .anchor-name {
            font-size: $bf-font-size-sm;
            margin-top: $bf-public-size-10;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 56px;
          }
        }
      }
    }
  }

  .empty {
    height: calc(100vh - 185px);
  }
}
</style>
