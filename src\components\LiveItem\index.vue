<template>
  <div class="live-item" @click="handleToRoom(liveData.matchId, liveData.userId, liveData.liveType)">
    <div class="live-cover">
      <!-- 直播封面 -->
      <img class="cover-image" :src="liveData.liveCover" :alt="liveData.liveTitle" />
      <!-- Live标识 -->
      <div class="live-badge">
        <img class="live-camera" src="@/static/live/camera-icon.png" />
        <span>Live</span>
      </div>
      <!-- 播放按钮 -->
      <div class="play-button">
        <img src="@/static/live/play-icon.png" alt="播放按钮" />
      </div>
    </div>
    <!-- 直播信息 -->
    <div class="live-info">
      <div class="info-box">
        <!-- 主播信息 -->
        <div class="anchor-info">
          <img class="anchor-avatar" :src="liveData.userImage" alt="主播头像" />
          <div class="anchor-name">{{ liveData.userName }}</div>
        </div>
        <!-- 观看人数 -->
        <div class="watch-count">
          <img class="hot" src="@/static/live/hot-icon.png" />
          {{ liveData.hotMax }}
        </div>
      </div>
      <p class="live-title">{{ liveData.liveTitle }}</p>
    </div>
  </div>
</template>

<script lang="ts" setup>
interface LiveData {
  matchId: number
  liveTitle: string
  liveCover: string
  userName: string
  userImage: string
  hotMax: number
  userId: number
  liveType: string
  url?: string
  sclassName: string
  homeName: string
  awayName: string
  liveTypeEnum: string
}

interface Props {
  liveData: LiveData
}

defineProps<Props>()

const router = useRouter()

// 跳转直播间
const handleToRoom = (matchId: number, userId: number, liveType: string) => {
  router.push(`/room?matchId=${matchId}&userId=${userId}&liveTypeEnum=${liveType}`)
}
</script>

<style lang="scss" scoped>
.live-item {
  width: 186px;
  height: 148px;
  border-radius: $bf-public-size-4;
  cursor: pointer;
  background-color: $bf-bg-white;
  overflow: hidden;

  .live-cover {
    position: relative;
    width: 100%;
    height: 104px;
    overflow: hidden;

    .cover-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .live-badge {
      position: absolute;
      top: $bf-public-size-8;
      right: $bf-public-size-8;
      background: #FF2F2E;
      color: white;
      font-weight: 600;
      font-size: $bf-font-size-8;
      border-radius: $bf-public-size-2;
      padding: $bf-public-size-2 $bf-public-size-3;
      display: flex;
      align-items: center;
      gap: $bf-public-size-1;

      .live-camera {
        width: $bf-public-size-12;
        height: $bf-public-size-12;
      }
    }

    .play-button {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: $bf-public-size-30;
      height: $bf-public-size-30;
    }
  }

  .live-info {
    padding: $bf-public-size-4;

    .info-box {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .anchor-info {
        display: flex;
        align-items: center;

        .anchor-avatar {
          width: $bf-public-size-16;
          height: $bf-public-size-16;
          border-radius: 50%;
          margin-right: $bf-public-size-4;
        }

        .anchor-name {
          font-size: $bf-font-size-10;
          color: $bf-text-dark;
          line-height: $bf-public-size-22;
        }
      }

      .watch-count {
        font-size: $bf-font-size-10;
        color: $bf-text-dark;
        display: flex;
        align-items: center;
        gap: $bf-public-size-1;

        .hot {
          width: $bf-public-size-8;
          height: $bf-public-size-10;
        }
      }
    }

    .live-title {
      font-size: $bf-font-size-sm;
      color: $bf-text-dark;
      line-height: $bf-public-size-14;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>