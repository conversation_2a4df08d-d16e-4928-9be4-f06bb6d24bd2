<template>
  <div class="check-in-container">
    <!-- 日历式打卡区域 -->
    <div class="check-in-calendar">
      <div v-for="day in weekDays" :key="day.id" class="check-in-day" :class="{
        'active': day.checked,
        'current-day': day.isCurrentDay,
        'disabled': day.disabled
      }" @click="handleCheckIn(day)">
        <div class="day-name" :class="{ 'active': day.checked }">+{{ day.reward.amount }}</div>
        <div class="day-reward">
          <img v-if="day.checked" src="@/static/user/sign-success-icon.png" />
          <div class="day" v-else>{{ day.name }}</div>
        </div>
      </div>
    </div>
    <!-- 签到按钮 -->
    <van-button class="sign-btn" :disabled="currentDayChecked || !canCheckInToday" @click="handleSign">
      {{ currentDayChecked ? '今日已签到' : '立即签到' }}
    </van-button>
  </div>
</template>

<script lang="ts" setup>
// 类型定义
interface Reward {
  name: string
  amount: number
}

interface CheckInDay {
  id: number
  name: string
  checked: boolean
  isCurrentDay: boolean
  disabled: boolean
  reward?: Reward
}

// 奖励配置
const rewards: Reward[] = [
  { name: '经验', amount: 50 },
  { name: '经验', amount: 80 },
  { name: '经验', amount: 120 },
  { name: '经验', amount: 150 },
  { name: '经验', amount: 180 },
  { name: '经验', amount: 210 },
  { name: '经验', amount: 240 }
]

// 签到状态
const checkedDays = ref<number[]>([]) // 存储已签到的天数索引
const currentDayIndex = ref(0) // 当前应该签到的天数索引
const lastCheckInTimestamp = ref<number | null>(null) // 上次签到时间戳

// 生成一周的签到数据
const weekDays = computed<CheckInDay[]>(() => {
  return Array.from({ length: 7 }, (_, i) => {
    const isChecked = checkedDays.value.includes(i)
    const isCurrent = i === currentDayIndex.value
    const isPast = i < currentDayIndex.value
    return {
      id: i,
      name: `第${i + 1}天`,
      checked: isChecked,
      isCurrentDay: isCurrent,
      disabled: isPast && !isChecked,
      reward: rewards[i]
    }
  })
})

// 今天是否已签到
const currentDayChecked = computed(() => {
  return checkedDays.value.includes(currentDayIndex.value)
})

// 今天是否可以签到（防止未来日期签到）
const canCheckInToday = computed(() => {
  return weekDays.value[currentDayIndex.value].isCurrentDay
})

// 检查是否需要重置签到（断签）
const checkForReset = () => {
  if (!lastCheckInTimestamp.value) return
  const now = new Date()
  const lastCheckInDate = new Date(lastCheckInTimestamp.value)
  // 重置时间为当天零点
  const resetTime = new Date(lastCheckInDate)
  resetTime.setHours(24, 0, 0, 0)
  // 如果当前时间超过重置时间且未签到，则重置
  if (now > resetTime && !currentDayChecked.value) {
    checkedDays.value = []
    currentDayIndex.value = 0
  }
}

// 初始化签到数据
onMounted(() => {
  const savedData = localStorage.getItem('checkInData')
  if (savedData) {
    try {
      const { days, currentIndex, timestamp } = JSON.parse(savedData)
      checkedDays.value = days || []
      currentDayIndex.value = currentIndex || 0
      lastCheckInTimestamp.value = timestamp || null
      checkForReset()
    } catch (e) {
      console.error('Failed to parse check-in data', e)
      resetCheckInData()
    }
  }
})

// 签到处理
const handleCheckIn = (day: CheckInDay) => {
  if (day.disabled || day.checked || !day.isCurrentDay) return
  checkInDay(day.id)
}

const handleSign = () => {
  if (currentDayChecked.value || !canCheckInToday.value) return
  checkInDay(currentDayIndex.value)
}

const checkInDay = (dayIndex: number) => {
  // 添加到已签到列表
  checkedDays.value = [...checkedDays.value, dayIndex]
  // 更新当前签到索引（如果签到最后一天则循环）
  currentDayIndex.value = (dayIndex + 1) % 7
  // 记录签到时间
  lastCheckInTimestamp.value = Date.now()
  // 保存到本地存储
  saveCheckInData()
  // 发放奖励
  console.log(`获得奖励: ${rewards[dayIndex].amount} ${rewards[dayIndex].name}`)
}

// 保存签到数据
const saveCheckInData = () => {
  localStorage.setItem('checkInData', JSON.stringify({
    days: checkedDays.value,
    currentIndex: currentDayIndex.value,
    timestamp: lastCheckInTimestamp.value
  }))
}

// 重置签到数据
const resetCheckInData = () => {
  checkedDays.value = []
  currentDayIndex.value = 0
  lastCheckInTimestamp.value = null
  saveCheckInData()
}
</script>

<style lang="scss" scoped>
.check-in-container {

  .check-in-calendar {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;

    .check-in-day {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 6px;
      border-radius: 8px;
      background-color: #F2F1F0;

      &.active {
        background-color: #FFECEB;
      }

      &.current-day:not(.active) {
        background-color: #FFF8E6;
      }

      &.disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      .day-name {
        font-weight: 600;
        font-size: 12px;
        color: #818181;
        line-height: 24px;

        &.active {
          color: #FB2B1F;
        }
      }

      .day-reward {
        display: flex;
        align-items: center;
        font-size: 12px;

        img {
          width: 24px;
          height: 24px;
        }

        .day {
          font-size: 10px;
          color: #818181;
          line-height: 24px;
        }
      }
    }
  }

  .sign-btn {
    margin-top: 16px;
    width: 100%;
    background: #FB2B1F;
    border-radius: 32px;
    font-size: 14px;
    color: #FFFFFF;

    // &:disabled {
    //   background: #CCCCCC;
    //   cursor: not-allowed;
    // }
  }
}
</style>