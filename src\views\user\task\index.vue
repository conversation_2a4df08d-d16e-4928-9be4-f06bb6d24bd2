<template>
  <div class="task-page">
    <!-- 顶部背景区域 -->
    <div class="task-banner">
      <!-- 导航栏 -->
      <Navbar class="nav-bar" title="任务中心" :border="false" textColor="white" :height="68" />
      <!-- 用户信息 -->
      <div class="user-info">
        <img class="user-avatar" :src="userInfo.userImage" alt="用户头像" />
        <div class="user-details">
          <div class="user-name">{{ userInfo.userName }}</div>
          <div class="user-sign">
            <div class="sign-days">您已经签到{{ userInfo.exp }}天啦！</div>
            <div class="ticket-wrapper" @click="handleTicketDetails">
              <div class="ticket-btn">球票明细</div>
              <img class="right-arrow-icon" src="@/static/user/white-right-arrow-icon.png" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content-scroll">
      <!-- 签到卡片 -->
      <div class="sign-card">
        <header class="card-header">
          <div class="header-left">
            <img class="gift-icon" src="@/static/user/gift-icon.png" alt="礼物图标" />
            <span class="title">轻松签到拿经验与球票</span>
          </div>
          <div class="header-right">
            <span class="sign-remind">签到提醒</span>
            <van-switch v-model="checked" active-color="#FB2B1F" inactive-color="#dcdee0" size="16"
              @change="handleSignSwitch" />
          </div>
        </header>
        <p class="desc">连续签到不断签，经验更多</p>
        <!-- 7日打卡组件 -->
        <SevenDaySign @handleSign="handleSign" />
      </div>
      <!-- 新手奖励 -->
      <div class="reward-card">
        <p class="title">新手奖励</p>
        <div class="reward-box">
          <div class="card-item">
            <div class="reward-info">
              <p class="reward-title">首次登陆</p>
              <div class="ticket">
                <img class="ticket-icon" src="@/static/user/ticket-icon.png" alt="球票图标" />
                <span class="ticket-number">200球票</span>
              </div>
            </div>
            <div class="reward-status">已完成</div>
          </div>
          <div class="card-item">
            <div class="reward-info">
              <p class="reward-title">首次打赏主播</p>
              <div class="ticket">
                <img class="ticket-icon" src="@/static/user/ticket-icon.png" alt="球票图标" />
                <span class="ticket-number">50球票</span>
              </div>
            </div>
            <div class="reward-status">已完成</div>
          </div>
        </div>
      </div>
      <!-- 日常任务 -->
      <div class="task-card">
        <p class="title">日常任务</p>
        <div class="task-item" v-for="item in dailyTasksArray" :key="item.title">
          <div class="task-info">
            <img class="task-icon" :src="item.iconUrl" />
            <div>
              <p class="task-title">{{ item.title }}</p>
              <div class="ticket">
                <img class="ticket-icon" src="@/static/user/ticket-icon.png" alt="球票图标" />
                <span class="ticket-number">{{ `${item.ticket}球票` }}</span>
              </div>
            </div>
          </div>
          <div class="task-status" :class="item.status === 2 ? 'task-status-active' : ''">
            {{ item.status === 2 ? '已完成' : '去完成' }}
          </div>
        </div>
      </div>
      <!-- 邀请任务 -->
      <div class="task-card">
        <p class="title">邀请任务</p>
        <div class="task-item">
          <div class="task-info">
            <img class="task-icon" src="@/static/user/invite-icon.png" />
            <div>
              <p class="task-title">每邀请1人<span>（已邀请0人）</span></p>
              <div class="ticket">
                <span class="ticket-number ticket-text">注册成功，奖励</span>
                <img class="ticket-icon" src="@/static/user/ticket-icon.png" alt="球票图标" />
                <span class="ticket-number">100球票</span>
              </div>
            </div>
          </div>
          <div class="task-status" :class="false ? 'task-status-active' : ''">
            {{ false ? '已完成' : '去完成' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import ViewIcon from '@/static/user/view-icon.png'
import FollowIcon from '@/static/user/follow-icon.png'
import BarrageIcon from '@/static/user/barrage-icon.png'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()
const userInfo = computed(() => userStore.getUserInfo)

const dailyTasksArray = reactive([
  {
    iconUrl: ViewIcon,
    title: '观看时长达到5分钟',
    ticket: 10,
    status: 2
  },
  {
    iconUrl: FollowIcon,
    title: '每日关注',
    ticket: 10,
    status: 1
  },
  {
    iconUrl: BarrageIcon,
    title: '每日发送弹幕',
    ticket: 10,
    status: 1
  },
])

// 是否打开签到提醒
const checked = ref(true)

// 球票明细
const handleSignSwitch = () => { }

// 球票明细
const handleTicketDetails = () => {
  router.push('/ticket-details')
}

// 立即签到
const handleSign = () => { }
</script>

<style lang="scss" scoped>
@import './index.scss'
</style>